const { redis } = require('../config/redis')

/**
 * Redis缓存服务类
 * 提供统一的缓存操作接口，包含缓存键管理、数据序列化等功能
 */
class CacheService {
  constructor() {
    this.keyPrefix = 'manage_app:'
    this.defaultTTL = 3600 // 默认1小时过期
  }

  /**
   * 生成缓存键
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @returns {string} 完整的缓存键
   */
  generateKey(module, key) {
    return `${this.keyPrefix}${module}:${key}`
  }

  /**
   * 设置缓存
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @param {any} data - 要缓存的数据
   * @param {number} ttl - 过期时间（秒），默认使用defaultTTL
   * @returns {Promise<boolean>} 是否设置成功
   */
  async set(module, key, data, ttl = this.defaultTTL) {
    try {
      const cacheKey = this.generateKey(module, key)
      const serializedData = JSON.stringify(data)
      
      if (ttl > 0) {
        await redis.setex(cacheKey, ttl, serializedData)
      } else {
        await redis.set(cacheKey, serializedData)
      }
      
      console.log(`✅ 缓存设置成功: ${cacheKey}`)
      return true
    } catch (error) {
      console.error(`❌ 缓存设置失败: ${module}:${key}`, error.message)
      return false
    }
  }

  /**
   * 获取缓存
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @returns {Promise<any|null>} 缓存的数据，不存在返回null
   */
  async get(module, key) {
    try {
      const cacheKey = this.generateKey(module, key)
      const cachedData = await redis.get(cacheKey)
      
      if (cachedData) {
        console.log(`✅ 缓存命中: ${cacheKey}`)
        return JSON.parse(cachedData)
      }
      
      console.log(`⚠️ 缓存未命中: ${cacheKey}`)
      return null
    } catch (error) {
      console.error(`❌ 缓存获取失败: ${module}:${key}`, error.message)
      return null
    }
  }

  /**
   * 删除缓存
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @returns {Promise<boolean>} 是否删除成功
   */
  async delete(module, key) {
    try {
      const cacheKey = this.generateKey(module, key)
      const result = await redis.del(cacheKey)
      
      console.log(`✅ 缓存删除${result > 0 ? '成功' : '(键不存在)'}: ${cacheKey}`)
      return result > 0
    } catch (error) {
      console.error(`❌ 缓存删除失败: ${module}:${key}`, error.message)
      return false
    }
  }

  /**
   * 删除模块下的所有缓存
   * @param {string} module - 模块名
   * @returns {Promise<number>} 删除的键数量
   */
  async deleteByModule(module) {
    try {
      const pattern = this.generateKey(module, '*')
      const keys = await redis.keys(pattern)
      
      if (keys.length > 0) {
        const result = await redis.del(...keys)
        console.log(`✅ 批量删除缓存成功: ${module} (${result}个键)`)
        return result
      }
      
      console.log(`⚠️ 没有找到要删除的缓存: ${module}`)
      return 0
    } catch (error) {
      console.error(`❌ 批量删除缓存失败: ${module}`, error.message)
      return 0
    }
  }

  /**
   * 检查缓存是否存在
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(module, key) {
    try {
      const cacheKey = this.generateKey(module, key)
      const result = await redis.exists(cacheKey)
      return result === 1
    } catch (error) {
      console.error(`❌ 检查缓存存在性失败: ${module}:${key}`, error.message)
      return false
    }
  }

  /**
   * 设置缓存过期时间
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @param {number} ttl - 过期时间（秒）
   * @returns {Promise<boolean>} 是否设置成功
   */
  async expire(module, key, ttl) {
    try {
      const cacheKey = this.generateKey(module, key)
      const result = await redis.expire(cacheKey, ttl)
      return result === 1
    } catch (error) {
      console.error(`❌ 设置缓存过期时间失败: ${module}:${key}`, error.message)
      return false
    }
  }

  /**
   * 获取缓存剩余过期时间
   * @param {string} module - 模块名
   * @param {string} key - 键名
   * @returns {Promise<number>} 剩余秒数，-1表示永不过期，-2表示键不存在
   */
  async ttl(module, key) {
    try {
      const cacheKey = this.generateKey(module, key)
      return await redis.ttl(cacheKey)
    } catch (error) {
      console.error(`❌ 获取缓存过期时间失败: ${module}:${key}`, error.message)
      return -2
    }
  }

  /**
   * 生成分页缓存键
   * @param {string} module - 模块名
   * @param {string} baseKey - 基础键名
   * @param {number} page - 页码
   * @param {number} limit - 每页数量
   * @param {object} filters - 额外的过滤条件
   * @returns {string} 分页缓存键
   */
  generatePageKey(module, baseKey, page, limit, filters = {}) {
    const filterStr = Object.keys(filters).length > 0 
      ? '_' + Object.entries(filters).map(([k, v]) => `${k}:${v}`).join('_')
      : ''
    return `${baseKey}_page:${page}_limit:${limit}${filterStr}`
  }

  /**
   * 清除所有应用缓存
   * @returns {Promise<number>} 删除的键数量
   */
  async clearAll() {
    try {
      const pattern = `${this.keyPrefix}*`
      const keys = await redis.keys(pattern)
      
      if (keys.length > 0) {
        const result = await redis.del(...keys)
        console.log(`✅ 清除所有应用缓存成功 (${result}个键)`)
        return result
      }
      
      console.log(`⚠️ 没有找到要清除的应用缓存`)
      return 0
    } catch (error) {
      console.error(`❌ 清除所有应用缓存失败`, error.message)
      return 0
    }
  }
}

// 创建单例实例
const cacheService = new CacheService()

module.exports = cacheService
