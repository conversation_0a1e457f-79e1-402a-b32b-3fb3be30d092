// 认证相关工具函数
import { ElMessage } from 'element-plus'
import router from '../router'

// 全局状态管理
let isTokenExpired = false
let logoutTimer = null

/**
 * 处理token过期
 * @param {boolean} showMessage 是否显示提示消息
 */
export function handleTokenExpired(showMessage = true) {
  // 防止重复处理
  if (isTokenExpired) {
    return
  }
  
  isTokenExpired = true
  
  // 清除可能存在的定时器
  if (logoutTimer) {
    clearTimeout(logoutTimer)
  }
  
  console.log('Token已过期，开始清理...')
  
  // 清除本地存储
  localStorage.removeItem('token')
  localStorage.removeItem('userInfo')
  
  // 显示提示消息
  if (showMessage) {
    ElMessage.error('登录已过期，请重新登录')
  }
  
  // 延迟跳转，避免路由冲突
  logoutTimer = setTimeout(() => {
    const currentPath = router.currentRoute.value.fullPath
    if (currentPath !== '/login') {
      router.push({
        path: '/login',
        query: { redirect: currentPath }
      }).finally(() => {
        // 重置状态
        resetTokenExpiredState()
      })
    } else {
      resetTokenExpiredState()
    }
  }, 100)
}

/**
 * 重置token过期状态
 */
export function resetTokenExpiredState() {
  isTokenExpired = false
  if (logoutTimer) {
    clearTimeout(logoutTimer)
    logoutTimer = null
  }
}

/**
 * 检查是否正在处理token过期
 */
export function isHandlingTokenExpired() {
  return isTokenExpired
}

/**
 * 安全的消息提示（避免重复提示）
 */
let lastMessageTime = 0
let lastMessageText = ''

export function safeMessage(message, type = 'error', duration = 3000) {
  const now = Date.now()
  
  // 如果是相同的消息且在3秒内，则不重复显示
  if (message === lastMessageText && now - lastMessageTime < 3000) {
    return
  }
  
  lastMessageTime = now
  lastMessageText = message
  
  ElMessage({
    message,
    type,
    duration
  })
}
