{"name": "backend", "version": "1.0.0", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "node-fetch": "^2.7.0"}, "devDependencies": {"nodemon": "^3.1.10"}, "description": ""}