/**
 * 更新管理员密码为加密格式
 * 用于将数据库中的明文密码更新为bcrypt加密密码
 */

const bcrypt = require('bcryptjs')
require('dotenv').config()
const { db } = require('../config/database')

async function updateAdminPassword() {
  try {
    console.log('开始更新管理员密码...')
    
    // 获取所有管理员
    const adminCollection = db.collection('admin')
    const admins = await adminCollection.get()
    
    if (admins.data.length === 0) {
      console.log('没有找到管理员账号')
      return
    }
    
    for (const admin of admins.data) {
      // 检查密码是否已经是加密格式
      if (admin.pw.startsWith('$2a$') || admin.pw.startsWith('$2b$') || admin.pw.startsWith('$2y$')) {
        console.log(`管理员 ${admin.account} 的密码已经是加密格式，跳过`)
        continue
      }
      
      // 加密明文密码
      const hashedPassword = await bcrypt.hash(admin.pw, 12)
      
      // 更新数据库
      await adminCollection.doc(admin._id).update({
        pw: hashedPassword,
        updateTime: new Date()
      })
      
      console.log(`管理员 ${admin.account} 的密码已更新为加密格式`)
      console.log(`原密码: ${admin.pw}`)
      console.log(`新密码: ${hashedPassword}`)
      console.log('---')
    }
    
    console.log('所有管理员密码更新完成！')
    
  } catch (error) {
    console.error('更新密码失败:', error)
  }
}

// 运行更新
updateAdminPassword()
