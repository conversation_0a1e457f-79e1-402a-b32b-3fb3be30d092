const express = require('express')
const router = express.Router()
const dashboardController = require('../controllers/dashboardController')
// const { authMiddleware, checkPermission } = require('../middleware/auth')

// 临时移除认证中间件，方便开发测试
// router.use(authMiddleware)

// 仪表盘数据路由
router.get('/stats', dashboardController.getStats)

// 用户数据路由
router.get('/users', dashboardController.getAllUsers)
router.get('/users/:userId', dashboardController.getUserById)
router.put('/users/:userId', dashboardController.updateUser)

// 积分管理路由
router.get('/points/search-user', dashboardController.searchUserForPoints)
router.post('/points/update-user', dashboardController.updateUserPoints)
router.post('/points/batch-update', dashboardController.batchUpdatePoints)

module.exports = router