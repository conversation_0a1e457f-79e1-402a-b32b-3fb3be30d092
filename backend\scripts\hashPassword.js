/**
 * 密码加密工具
 * 用于生成管理员账号的加密密码
 * 
 * 使用方法：
 * node scripts/hashPassword.js your_password
 */

const bcrypt = require('bcryptjs')

// 获取命令行参数
const password = process.argv[2]

if (!password) {
  console.error('请提供要加密的密码')
  console.log('使用方法: node scripts/hashPassword.js your_password')
  process.exit(1)
}

async function hashPassword() {
  try {
    const hashedPassword = await bcrypt.hash(password, 12)
    console.log('原始密码:', password)
    console.log('加密后的密码:', hashedPassword)
    console.log('\n请在腾讯云开发数据库中创建admin集合，并添加以下文档:')
    console.log(JSON.stringify({
      account: "admin",
      pw: hashedPassword,
      name: "管理员",
      createTime: new Date(),
      updateTime: new Date()
    }, null, 2))
  } catch (error) {
    console.error('密码加密失败:', error)
  }
}

hashPassword()
