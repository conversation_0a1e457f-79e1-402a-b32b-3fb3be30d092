<template>
  <div class="layout-container">
    <!-- 移动端遮罩层 -->
    <div 
      v-if="isMobile && !isCollapse" 
      class="mobile-overlay"
      @click="handleMobileOverlayClick"
    ></div>
    
    <!-- 侧边栏 - 只在已登录时显示 -->
    <Sidebar v-if="isLoggedIn" :is-mobile="isMobile" />
    
    <!-- 主内容区域 -->
    <div class="main-container" :class="{
      'is-collapse': isCollapse && isLoggedIn,
      'is-mobile': isMobile,
      'no-sidebar': !isLoggedIn
    }">
      <!-- 顶部导航 - 只在已登录时显示 -->
      <Header v-if="isLoggedIn" :is-mobile="isMobile" />
      
      <!-- 页面内容 -->
      <el-main class="page-main" :class="{ 'no-header': !isLoggedIn }">
        <div class="content-wrapper" :class="{ 'login-wrapper': !isLoggedIn }">
          <router-view v-slot="{ Component, route }">
            <transition name="fade-slide" mode="out-in">
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </div>
      </el-main>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useLayoutStore, useUserStore } from '../../stores'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'

const layoutStore = useLayoutStore()
const userStore = useUserStore()
const isCollapse = computed(() => layoutStore.isCollapse)
const isLoggedIn = computed(() => userStore.isLoggedIn)

// 移动端检测
const isMobile = ref(false)

const checkIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
  // 移动端默认折叠侧边栏
  if (isMobile.value && !isCollapse.value) {
    layoutStore.setCollapse(true)
  }
}

const handleResize = () => {
  checkIsMobile()
}

// 移动端遮罩层点击处理
const handleMobileOverlayClick = () => {
  if (isMobile.value) {
    layoutStore.setCollapse(true)
  }
}

onMounted(() => {
  checkIsMobile()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.layout-container {
  height: 100vh;
  width: 100%;
  display: flex;
}

.main-container {
  flex: 1;
  min-height: 100vh;
  transition: margin-left 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  background-color: #f0f2f5;
  margin-left: 240px;
  overflow: auto;
  min-width: 0;
}

.main-container.is-collapse {
  margin-left: 64px;
}

.main-container.no-sidebar {
  margin-left: 0;
}

.page-main {
  padding: 0 !important;
  min-height: calc(100vh - 60px);
  margin: 0;
}

.page-main.no-header {
  min-height: 100vh;
}

.content-wrapper {
  background-color: #fff;
  border-radius: 0;
  padding: 24px;
  box-shadow: none;
  min-height: calc(100vh - 60px);
  margin: 0;
  width: 100%;
  height: 100%;
}

.content-wrapper.login-wrapper {
  background-color: transparent;
  padding: 0;
  min-height: 100vh;
}

/* 覆盖Element Plus的默认样式 */
:deep(.el-main) {
  padding: 0 !important;
  margin: 0;
}

/* 页面切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

/* 移动端遮罩层 */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1999;
  transition: opacity 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-container {
    margin-left: 0 !important;
    width: 100%;
  }
  
  .main-container.is-collapse {
    margin-left: 0 !important;
  }
  
  .main-container.is-mobile {
    margin-left: 0 !important;
  }
  
  .content-wrapper {
    padding: 16px !important;
    border-radius: 0;
  }
  
  .page-main {
    min-height: calc(100vh - 60px);
  }
}

/* 平板设备 */
@media (min-width: 769px) and (max-width: 1024px) {
  .content-wrapper {
    padding: 20px;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .content-wrapper {
    padding: 12px !important;
  }
}


</style>