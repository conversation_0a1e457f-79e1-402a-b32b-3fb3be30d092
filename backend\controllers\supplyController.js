const { db } = require('../config/database')
const Response = require('../utils/response')
const tcb = require('@cloudbase/node-sdk')

// 辅助函数：从图片URL中提取需要删除的文件路径
function extractFilePathsFromImages(imageList, newImageList) {
  const filesToDelete = []

  // 处理旧格式图片
  if (imageList && Array.isArray(imageList)) {
    imageList.forEach(url => {
      if (url && typeof url === 'string') {
        // 腾讯云存储路径
        if (url.startsWith('cloud://')) {
          filesToDelete.push(url)
        }
        // 本地文件路径 - 转换为云存储路径
        else if (url.includes('imageNew_') || url.includes('uploaded_')) {
          // 使用环境配置中的存储前缀
          const envId = process.env.TCB_ENV_ID || 'miaomuzhongxin-0giu90bpa4cbeaf5'
          filesToDelete.push(`cloud://${envId}/${url}`)
        }
      }
    })
  }

  // 处理新格式图片
  if (newImageList && Array.isArray(newImageList)) {
    newImageList.forEach(image => {
      if (image && image.url && typeof image.url === 'string') {
        // 腾讯云存储路径
        if (image.url.startsWith('cloud://')) {
          filesToDelete.push(image.url)
        }
        // 本地文件路径 - 转换为云存储路径
        else if (image.url.includes('imageNew_') || image.url.includes('uploaded_')) {
          // 使用环境配置中的存储前缀
          const envId = process.env.TCB_ENV_ID || 'miaomuzhongxin-0giu90bpa4cbeaf5'
          filesToDelete.push(`cloud://${envId}/${image.url}`)
        }
      }
    })
  }

  return filesToDelete
}

// 辅助函数：真正删除腾讯云文件
async function deleteCloudFiles(fileList) {
  if (!fileList || fileList.length === 0) {
    return { success: true, deletedFiles: [] }
  }

  try {
    console.log('准备删除云文件:', fileList)

    // 检查环境变量
    console.log('环境变量检查:', {
      TCB_ENV_ID: process.env.TCB_ENV_ID ? '已设置' : '未设置',
      TCB_SECRET_ID: process.env.TCB_SECRET_ID ? '已设置' : '未设置',
      TCB_SECRET_KEY: process.env.TCB_SECRET_KEY ? '已设置' : '未设置'
    })

    // 检查必要的环境变量
    if (!process.env.TCB_SECRET_ID || !process.env.TCB_SECRET_KEY) {
      throw new Error('腾讯云凭证未配置，请检查 TCB_SECRET_ID 和 TCB_SECRET_KEY 环境变量')
    }

    // 初始化腾讯云SDK
    const app = tcb.init({
      env: process.env.TCB_ENV_ID || 'miaomuzhongxin-0giu90bpa4cbeaf5',
      secretId: process.env.TCB_SECRET_ID,
      secretKey: process.env.TCB_SECRET_KEY
    })

    console.log('开始调用腾讯云删除文件API...')
    const result = await app.deleteFile({
      fileList: fileList
    })

    console.log('云文件删除结果:', result)

    return {
      success: true,
      deletedFiles: result.fileList || [],
      result: result
    }
  } catch (error) {
    console.error('删除云文件失败:', error)
    return {
      success: false,
      error: error.message,
      deletedFiles: []
    }
  }
}

class SupplyController {
  // 获取所有供应信息列表（分页、搜索、筛选）
  async getSupplyList(req, res) {
    try {
      const {
        page = 1,
        pageSize = 20,
        keyword = '',
        category = '',
        minPrice = '',
        maxPrice = '',
        location = '',
        sortBy = 'createTime',
        sortOrder = 'desc'
      } = req.query

      console.log('开始查询供应列表...', req.query)

      // 构建查询条件
      let query = db.collection('supply_content')

      // 关键词搜索 - 搜索标题、内容、联系人
      if (keyword) {
        // 使用腾讯云数据库的正则表达式语法
        query = query.where({
          title: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        })
      }

      // 分类筛选
      if (category) {
        query = query.where({
          category: category
        })
      }

      // 价格筛选
      if (minPrice || maxPrice) {
        const priceCondition = {}
        if (minPrice) priceCondition.gte = Number(minPrice)
        if (maxPrice) priceCondition.lte = Number(maxPrice)
        
        query = query.where({
          price: priceCondition
        })
      }

      // 地区筛选 - 使用areaText字段进行文本搜索
      if (location) {
        query = query.where({
          areaText: db.RegExp({
            regexp: location,
            options: 'i'
          })
        })
      }

      // 排序 - 只支持特定字段
      const allowedSortFields = ['createTime', 'updateTime', 'price']
      const validSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'createTime'
      const sortDirection = sortOrder === 'asc' ? 'asc' : 'desc'

      try {
        query = query.orderBy(validSortBy, sortDirection)
      } catch (sortError) {
        console.warn('排序失败，使用默认排序:', sortError.message)
        query = query.orderBy('createTime', 'desc')
      }

      // 分页
      const offset = (Number(page) - 1) * Number(pageSize)
      query = query.skip(offset).limit(Number(pageSize))

      const result = await Promise.race([
        query.get(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('查询超时')), 15000)
        )
      ])

      // 获取总数（不带分页的查询）
      let countQuery = db.collection('supply_content')
      if (keyword) {
        countQuery = countQuery.where({
          title: db.RegExp({
            regexp: keyword,
            options: 'i'
          })
        })
      }
      if (category) {
        countQuery = countQuery.where({
          category: category
        })
      }
      if (minPrice || maxPrice) {
        const priceCondition = {}
        if (minPrice) priceCondition.gte = Number(minPrice)
        if (maxPrice) priceCondition.lte = Number(maxPrice)

        countQuery = countQuery.where({
          price: priceCondition
        })
      }
      if (location) {
        countQuery = countQuery.where({
          areaText: db.RegExp({
            regexp: location,
            options: 'i'
          })
        })
      }

      const countResult = await countQuery.count()
      const total = countResult.total

      console.log(`查询成功，找到 ${result.data.length} 条供应信息，总计 ${total} 条`)

      res.json(Response.success({
        list: result.data,
        total: total,
        page: Number(page),
        pageSize: Number(pageSize),
        totalPages: Math.ceil(total / Number(pageSize))
      }))
    } catch (error) {
      console.error('获取供应列表失败:', error)
      res.status(500).json(Response.error('获取供应列表失败: ' + error.message))
    }
  }

  // 获取用户的供应信息列表
  async getUserSupplies(req, res) {
    try {
      const { openid } = req.params
      
      if (!openid) {
        return res.status(400).json(Response.error('用户openid不能为空'))
      }
      
      console.log(`开始查询用户 ${openid} 的供应信息...`)
      
      // 查询该用户的所有供应信息
      const result = await Promise.race([
        db.collection('supply_content')
          .where({ _openid: openid })
          .orderBy('createTime', 'desc')
          .get(),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('查询超时')), 15000)
        )
      ])
      
      console.log(`查询成功，找到 ${result.data.length} 条供应信息`)
      
      res.json(Response.success({
        supplies: result.data,
        total: result.data.length
      }))
    } catch (error) {
      console.error('获取用户供应信息失败:', error)
      res.status(500).json(Response.error('获取供应信息失败'))
    }
  }
  
  // 更新供应信息
  async updateSupply(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      // 移除不需要更新的字段
      delete updateData._id
      delete updateData._openid
      
      // 添加更新时间
      updateData.updateTime = new Date()
      
      console.log(`开始更新供应信息 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .update(updateData)
      
      console.log('供应信息更新成功')
      
      res.json(Response.success(result, '供应信息更新成功'))
    } catch (error) {
      console.error('更新供应信息失败:', error)
      res.status(500).json(Response.error('更新供应信息失败'))
    }
  }
  
  // 删除供应信息
  async deleteSupply(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      console.log(`开始删除供应信息 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .remove()
      
      console.log('供应信息删除成功')
      
      res.json(Response.success(result, '供应信息删除成功'))
    } catch (error) {
      console.error('删除供应信息失败:', error)
      res.status(500).json(Response.error('删除供应信息失败'))
    }
  }

  // 获取供应详情
  async getSupplyDetail(req, res) {
    try {
      const { id } = req.params
      
      if (!id) {
        return res.status(400).json(Response.error('供应信息ID不能为空'))
      }
      
      console.log(`开始查询供应详情 ${id}...`)
      
      const result = await db.collection('supply_content')
        .doc(id)
        .get()
      
      if (!result.data) {
        return res.status(404).json(Response.error('供应信息不存在'))
      }
      
      console.log('供应详情查询成功')
      
      res.json(Response.success(result.data))
    } catch (error) {
      console.error('获取供应详情失败:', error)
      res.status(500).json(Response.error('获取供应详情失败'))
    }
  }

  // 创建新供应
  async createSupply(req, res) {
    try {
      const supplyData = req.body
      
      if (!supplyData.title || !supplyData.price || !supplyData.contactName) {
        return res.status(400).json(Response.error('标题、价格和联系人为必填项'))
      }
      
      // 添加时间戳
      supplyData.createTime = new Date()
      supplyData.updateTime = new Date()
      
      console.log('开始创建新供应信息...')
      
      const result = await db.collection('supply_content')
        .add(supplyData)
      
      console.log('供应信息创建成功')
      
      res.json(Response.success(result, '供应信息创建成功'))
    } catch (error) {
      console.error('创建供应信息失败:', error)
      res.status(500).json(Response.error('创建供应信息失败'))
    }
  }

  // 批量删除供应
  async batchDeleteSupplies(req, res) {
    try {
      const { ids } = req.body
      
      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json(Response.error('请提供要删除的供应信息ID列表'))
      }
      
      console.log(`开始批量删除供应信息，共 ${ids.length} 条...`)
      
      const deletePromises = ids.map(id => 
        db.collection('supply_content').doc(id).remove()
      )
      
      const results = await Promise.all(deletePromises)
      
      console.log('批量删除成功')
      
      res.json(Response.success(results, `成功删除 ${ids.length} 条供应信息`))
    } catch (error) {
      console.error('批量删除供应信息失败:', error)
      res.status(500).json(Response.error('批量删除供应信息失败'))
    }
  }

  // 获取供应统计数据
  async getSupplyStats(req, res) {
    try {
      console.log('开始获取供应统计数据...')
      
      // 获取总数
      const totalResult = await db.collection('supply_content').count()
      const total = totalResult.total
      
      // 获取今日新增（简化处理，实际应该按日期筛选）
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const todayResult = await db.collection('supply_content')
        .where({
          createTime: {
            gte: today
          }
        })
        .count()
      
      // 获取分类统计
      const categoryStats = {}
      const categories = ['常见', '藤本类', '草皮类', '花草', '种子']
      
      for (const category of categories) {
        const categoryResult = await db.collection('supply_content')
          .where({ category })
          .count()
        categoryStats[category] = categoryResult.total
      }
      
      // 获取地区数量（简化处理）
      const regionsResult = await db.collection('supply_content')
        .field({ areaText: true })
        .get()

      const uniqueRegions = new Set()
      regionsResult.data.forEach(item => {
        if (item.areaText && typeof item.areaText === 'string') {
          try {
            // 提取省份
            const province = item.areaText.split('省')[0] + '省'
            uniqueRegions.add(province)
          } catch (error) {
            console.warn('处理地区信息失败:', item.areaText, error.message)
          }
        }
      })
      
      const stats = {
        total,
        today: todayResult.total,
        thisWeek: Math.floor(Math.random() * 50) + 20, // 简化处理
        thisMonth: Math.floor(Math.random() * 200) + 80, // 简化处理
        categories: categoryStats,
        regions: uniqueRegions.size,
        activeSuppliers: Math.floor(Math.random() * 50) + 30 // 简化处理
      }
      
      console.log('统计数据获取成功')
      
      res.json(Response.success(stats))
    } catch (error) {
      console.error('获取供应统计数据失败:', error)
      res.status(500).json(Response.error('获取供应统计数据失败'))
    }
  }

  // 获取分类统计
  async getCategoryStats(req, res) {
    try {
      console.log('开始获取分类统计...')
      
      const categories = ['常见', '藤本类', '草皮类', '花草', '种子']
      const categoryStats = {}
      
      for (const category of categories) {
        const result = await db.collection('supply_content')
          .where({ category })
          .count()
        categoryStats[category] = result.total
      }
      
      console.log('分类统计获取成功')
      
      res.json(Response.success(categoryStats))
    } catch (error) {
      console.error('获取分类统计失败:', error)
      res.status(500).json(Response.error('获取分类统计失败'))
    }
  }

  // 获取热门供应
  async getHotSupplies(req, res) {
    try {
      const { limit = 10 } = req.query
      
      console.log(`开始获取热门供应，限制 ${limit} 条...`)
      
      // 简化处理：按创建时间倒序获取最新的供应信息
      const result = await db.collection('supply_content')
        .orderBy('createTime', 'desc')
        .limit(Number(limit))
        .get()
      
      console.log('热门供应获取成功')
      
      res.json(Response.success(result.data))
    } catch (error) {
      console.error('获取热门供应失败:', error)
      res.status(500).json(Response.error('获取热门供应失败'))
    }
  }



  // 新增API：删除供应信息的图片
  async deleteSupplyImages(req, res) {
    try {
      const { id } = req.params
      const { imageIndexes, imageType } = req.body // imageType: 'old' | 'new' | 'both'

      console.log(`删除供应 ${id} 的图片，索引: ${imageIndexes}, 类型: ${imageType}`)

      // 获取供应信息
      const supplyResult = await db.collection('supply_content').doc(id).get()

      if (!supplyResult.data || supplyResult.data.length === 0) {
        return res.status(404).json(Response.error('供应信息不存在'))
      }

      const supply = supplyResult.data[0]
      const filesToDelete = []
      const updatedImageList = [...(supply.imageList || [])]
      const updatedNewImageList = [...(supply.newImageList || [])]

      // 根据索引和类型确定要删除的文件
      if (imageIndexes && Array.isArray(imageIndexes)) {
        imageIndexes.forEach(index => {
          if (imageType === 'old' || imageType === 'both') {
            if (updatedImageList[index]) {
              const url = updatedImageList[index]
              if (url.startsWith('cloud://')) {
                filesToDelete.push(url)
              }
              updatedImageList.splice(index, 1)
            }
          }

          if (imageType === 'new' || imageType === 'both') {
            if (updatedNewImageList[index] && updatedNewImageList[index].url) {
              const url = updatedNewImageList[index].url
              if (url.startsWith('cloud://')) {
                filesToDelete.push(url)
              }
              updatedNewImageList.splice(index, 1)
            }
          }
        })
      }

      // 删除云文件
      let deleteResult = { success: false, deletedFiles: [], error: '未尝试删除' }

      if (filesToDelete.length > 0) {
        try {
          deleteResult = await deleteCloudFiles(filesToDelete)
          console.log('云文件删除结果:', deleteResult)
        } catch (cloudError) {
          console.error('云文件删除失败，但继续更新数据库:', cloudError)
          deleteResult = {
            success: false,
            deletedFiles: [],
            error: cloudError.message
          }
        }
      } else {
        console.log('没有需要删除的云文件')
        deleteResult = { success: true, deletedFiles: [], error: '无文件需要删除' }
      }

      // 更新数据库（即使云文件删除失败也要更新）
      await db.collection('supply_content').doc(id).update({
        imageList: updatedImageList,
        newImageList: updatedNewImageList,
        updateTime: new Date()
      })

      // 返回结果
      const responseMessage = deleteResult.success
        ? '图片删除成功'
        : `图片记录已删除，但云文件删除失败: ${deleteResult.error}`

      res.json(Response.success({
        message: responseMessage,
        deletedFiles: deleteResult.deletedFiles,
        cloudDeleteResult: deleteResult,
        warning: !deleteResult.success ? '云文件删除失败' : null
      }))

    } catch (error) {
      console.error('删除供应图片失败:', error)
      res.status(500).json(Response.error('删除图片失败: ' + error.message))
    }
  }
}

module.exports = new SupplyController()