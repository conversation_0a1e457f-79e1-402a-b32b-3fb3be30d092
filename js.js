/**
   * 上传图片到云存储
   * @param {String} userId 用户ID
   * @param {String} postId 帖子ID
   * @returns {Promise<Array>} 返回上传成功的文件ID数组
   */
uploadImages(userId, postId) {
    const { newImageList } = this.data;

    // 如果没有图片，直接返回空数组
    if (newImageList.length === 0) {
      return Promise.resolve([]);
    }
    
    wx.showLoading({
      title: '上传图片中...',
      mask: true
    });
    
    // 创建文件夹路径
    const folderPath = `supply_images/${userId}_${postId}`;
    
    // 创建上传任务列表
    const uploadTasks = newImageList.map((imageItem, index) => {
      return new Promise((resolve, reject) => {
        // 获取文件扩展名
        const extension = imageItem.url.match(/\.([^.]+)$/)?.[1] || 'jpg';

        // 使用uniqueId生成唯一文件名
        const fileId = imageItem.uniqueId || `${Date.now()}_${index}`;
        const cloudPath = `${folderPath}/imageNew_${fileId}.${extension}`;

        // 上传文件
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: imageItem.url,
          success: res => {
            // 返回包含完整信息的对象
            resolve({
              url: res.fileID,
              captureTime: imageItem.captureTime,
              uniqueId: imageItem.uniqueId
            });
          },
          fail: err => {
            console.error('上传图片失败:', err);
            reject(err);
          }
        });
      });
    });
    
    // 等待所有上传任务完成
    return Promise.all(uploadTasks);
  },
