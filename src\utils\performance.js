/**
 * 性能优化工具类
 * 提供缓存管理、防抖、节流等功能
 */

class CacheManager {
  constructor() {
    this.cache = new Map()
    this.expiryTimes = new Map()
    this.maxSize = 100 // 最大缓存条目数
  }

  /**
   * 设置缓存
   * @param {string} key 缓存键
   * @param {any} value 缓存值
   * @param {number} expiry 过期时间（毫秒）
   */
  set(key, value, expiry = 5 * 60 * 1000) {
    // 检查缓存大小限制
    if (this.cache.size >= this.maxSize) {
      this.cleanup()
    }

    this.cache.set(key, value)
    this.expiryTimes.set(key, Date.now() + expiry)
  }

  /**
   * 获取缓存
   * @param {string} key 缓存键
   * @returns {any|null} 缓存值或null
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null
    }

    const expiryTime = this.expiryTimes.get(key)
    if (Date.now() > expiryTime) {
      this.delete(key)
      return null
    }

    return this.cache.get(key)
  }

  /**
   * 删除缓存
   * @param {string} key 缓存键
   */
  delete(key) {
    this.cache.delete(key)
    this.expiryTimes.delete(key)
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.cache.clear()
    this.expiryTimes.clear()
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, expiryTime] of this.expiryTimes) {
      if (now > expiryTime) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))

    // 如果清理后仍然超过限制，删除最旧的条目
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.cache.keys().next().value
      this.delete(oldestKey)
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys())
    }
  }
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastCall = 0
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 批量操作工具
 * 将多个操作合并为一次执行
 */
export class BatchProcessor {
  constructor(batchSize = 10, delay = 100) {
    this.batchSize = batchSize
    this.delay = delay
    this.queue = []
    this.timeoutId = null
  }

  add(item) {
    this.queue.push(item)
    
    if (this.queue.length >= this.batchSize) {
      this.flush()
    } else if (!this.timeoutId) {
      this.timeoutId = setTimeout(() => this.flush(), this.delay)
    }
  }

  flush() {
    if (this.queue.length === 0) return

    const batch = [...this.queue]
    this.queue = []
    
    if (this.timeoutId) {
      clearTimeout(this.timeoutId)
      this.timeoutId = null
    }

    return batch
  }
}

/**
 * 虚拟滚动工具
 * 用于大数据量列表的性能优化
 */
export class VirtualScroller {
  constructor(options = {}) {
    this.itemHeight = options.itemHeight || 50
    this.containerHeight = options.containerHeight || 400
    this.buffer = options.buffer || 5
    this.data = options.data || []
  }

  getVisibleRange(scrollTop) {
    const start = Math.floor(scrollTop / this.itemHeight)
    const visibleCount = Math.ceil(this.containerHeight / this.itemHeight)
    const end = start + visibleCount

    return {
      start: Math.max(0, start - this.buffer),
      end: Math.min(this.data.length, end + this.buffer),
      offsetY: start * this.itemHeight
    }
  }

  getVisibleData(scrollTop) {
    const range = this.getVisibleRange(scrollTop)
    return {
      items: this.data.slice(range.start, range.end),
      range,
      totalHeight: this.data.length * this.itemHeight
    }
  }
}

/**
 * 图片懒加载工具
 */
export class ImageLazyLoader {
  constructor() {
    this.observer = null
    this.loadedImages = new Set()
    this.init()
  }

  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: '50px',
          threshold: 0.1
        }
      )
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.loadImage(entry.target)
        this.observer.unobserve(entry.target)
      }
    })
  }

  loadImage(img) {
    const src = img.dataset.src
    if (src && !this.loadedImages.has(src)) {
      img.src = src
      this.loadedImages.add(src)
      img.classList.add('loaded')
    }
  }

  observe(img) {
    if (this.observer) {
      this.observer.observe(img)
    } else {
      // 降级处理
      this.loadImage(img)
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }
}

/**
 * 性能监控工具
 */
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      apiCalls: [],
      renderTimes: [],
      errors: []
    }
  }

  startTimer(name) {
    return {
      name,
      startTime: performance.now(),
      end: () => {
        const endTime = performance.now()
        const duration = endTime - this.startTime
        this.recordMetric('renderTimes', { name, duration, timestamp: Date.now() })
        return duration
      }
    }
  }

  recordApiCall(url, method, duration, status) {
    this.metrics.apiCalls.push({
      url,
      method,
      duration,
      status,
      timestamp: Date.now()
    })

    // 保持最近100条记录
    if (this.metrics.apiCalls.length > 100) {
      this.metrics.apiCalls.shift()
    }
  }

  recordError(error, context) {
    this.metrics.errors.push({
      message: error.message,
      stack: error.stack,
      context,
      timestamp: Date.now()
    })

    // 保持最近50条错误记录
    if (this.metrics.errors.length > 50) {
      this.metrics.errors.shift()
    }
  }

  recordMetric(type, data) {
    if (!this.metrics[type]) {
      this.metrics[type] = []
    }
    this.metrics[type].push(data)
  }

  getMetrics() {
    return { ...this.metrics }
  }

  getAverageApiTime() {
    const calls = this.metrics.apiCalls
    if (calls.length === 0) return 0
    
    const total = calls.reduce((sum, call) => sum + call.duration, 0)
    return total / calls.length
  }

  getAverageRenderTime() {
    const renders = this.metrics.renderTimes
    if (renders.length === 0) return 0
    
    const total = renders.reduce((sum, render) => sum + render.duration, 0)
    return total / renders.length
  }

  clearMetrics() {
    this.metrics = {
      apiCalls: [],
      renderTimes: [],
      errors: []
    }
  }
}

// 创建全局实例
export const cacheManager = new CacheManager()
export const performanceMonitor = new PerformanceMonitor()

// 导出默认配置
export const defaultConfig = {
  cache: {
    expiry: 5 * 60 * 1000, // 5分钟
    maxSize: 100
  },
  debounce: {
    delay: 300
  },
  throttle: {
    delay: 300
  },
  virtualScroll: {
    itemHeight: 50,
    buffer: 5
  }
}

/**
 * 内存使用监控
 */
export function getMemoryUsage() {
  if ('memory' in performance) {
    return {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
    }
  }
  return null
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 图片压缩工具
 */
export function compressImage(file, quality = 0.8, maxWidth = 1920) {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      const ratio = Math.min(maxWidth / img.width, maxWidth / img.height)
      canvas.width = img.width * ratio
      canvas.height = img.height * ratio
      
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
      
      canvas.toBlob(resolve, 'image/jpeg', quality)
    }
    
    img.src = URL.createObjectURL(file)
  })
}