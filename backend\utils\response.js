// 统一响应格式
class Response {
  static success(data = null, message = '操作成功') {
    return {
      code: 200,
      message,
      data,
      timestamp: new Date().toISOString()
    }
  }

  static error(message = '操作失败', code = 500, data = null) {
    return {
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    }
  }

  static paginate(data, total, page, pageSize) {
    return {
      code: 200,
      message: '获取成功',
      data: {
        list: data,
        pagination: {
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(total / pageSize)
        }
      },
      timestamp: new Date().toISOString()
    }
  }
}

module.exports = Response