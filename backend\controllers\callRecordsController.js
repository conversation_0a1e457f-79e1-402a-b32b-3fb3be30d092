const { db } = require('../config/database')
const Response = require('../utils/response')

/**
 * 获取通话记录列表
 */
const getCallRecords = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      keyword = '', 
      postType = '', 
      sortBy = 'callTime', 
      sortOrder = 'desc' 
    } = req.query
    
    console.log(`获取通话记录请求: page=${page}, limit=${limit}, keyword=${keyword}, postType=${postType}`)
    
    const offset = (parseInt(page) - 1) * parseInt(limit)
    const limitNum = parseInt(limit)
    
    // 构建查询条件
    let query = db.collection('backendCallRecord')
    
    // 关键词搜索 - 支持用户ID、帖子标题、电话号码搜索
    if (keyword) {
      query = query.where(db.command.or([
        { userId: db.command.regex({ regexp: keyword, options: 'i' }) },
        { postTitle: db.command.regex({ regexp: keyword, options: 'i' }) },
        { phoneNumber: db.command.regex({ regexp: keyword, options: 'i' }) },
        { caller: db.command.regex({ regexp: keyword, options: 'i' }) }
      ]))
    }
    
    // 帖子类型筛选
    if (postType && ['supply', 'demand'].includes(postType)) {
      query = query.where({ postType })
    }
    
    // 排序
    const sortDirection = sortOrder === 'asc' ? 'asc' : 'desc'
    query = query.orderBy(sortBy, sortDirection)
    
    // 获取总数
    const countResult = await query.count()
    const total = countResult.total
    
    // 获取分页数据
    const result = await query.skip(offset).limit(limitNum).get()
    
    console.log(`通话记录查询结果: 总数=${total}, 当前页数据=${result.data.length}`)
    
    // 格式化数据
    const formattedData = result.data.map(record => ({
      ...record,
      // 格式化时间显示
      callTimeFormatted: record.callTime ? new Date(record.callTime).toLocaleString('zh-CN') : '',
      createTimeFormatted: record.createTime ? new Date(record.createTime).toLocaleString('zh-CN') : '',
      // 生成帖子类型显示文案
      postTypeDisplay: record.postType === 'demand' 
        ? `想卖《${record.postTitle || '未知'}》时拨打`
        : `想买《${record.postTitle || '未知'}》时拨打`
    }))
    
    res.json(Response.paginate(formattedData, total, page, limit))
    
  } catch (error) {
    console.error('获取通话记录失败:', error)
    res.status(500).json(Response.error('获取通话记录失败: ' + error.message))
  }
}

/**
 * 获取通话记录统计数据
 */
const getCallRecordsStats = async (req, res) => {
  try {
    console.log('获取通话记录统计数据...')
    
    // 获取总通话记录数
    const totalResult = await db.collection('backendCallRecord').count()
    const totalCalls = totalResult.total
    
    // 获取按帖子类型分组的统计
    const supplyResult = await db.collection('backendCallRecord')
      .where({ postType: 'supply' })
      .count()
    const demandResult = await db.collection('backendCallRecord')
      .where({ postType: 'demand' })
      .count()
    
    // 获取今日通话记录数
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const todayResult = await db.collection('backendCallRecord')
      .where({
        callTime: db.command.gte(today)
      })
      .count()
    
    // 获取本月通话记录数
    const thisMonth = new Date()
    thisMonth.setDate(1)
    thisMonth.setHours(0, 0, 0, 0)
    const monthResult = await db.collection('backendCallRecord')
      .where({
        callTime: db.command.gte(thisMonth)
      })
      .count()
    
    const stats = {
      totalCalls,
      supplyCalls: supplyResult.total,
      demandCalls: demandResult.total,
      todayCalls: todayResult.total,
      monthCalls: monthResult.total
    }
    
    console.log('通话记录统计:', stats)
    res.json(Response.success(stats, '获取统计数据成功'))
    
  } catch (error) {
    console.error('获取通话记录统计失败:', error)
    res.status(500).json(Response.error('获取统计数据失败: ' + error.message))
  }
}

/**
 * 获取通话记录详情
 */
const getCallRecordDetail = async (req, res) => {
  try {
    const { id } = req.params
    console.log('获取通话记录详情:', id)
    
    const result = await db.collection('backendCallRecord').doc(id).get()
    
    if (result.data.length > 0) {
      const record = result.data[0]
      
      // 格式化数据
      const formattedRecord = {
        ...record,
        callTimeFormatted: record.callTime ? new Date(record.callTime).toLocaleString('zh-CN') : '',
        createTimeFormatted: record.createTime ? new Date(record.createTime).toLocaleString('zh-CN') : '',
        postTypeDisplay: record.postType === 'demand' 
          ? `想卖《${record.postTitle || '未知'}》时拨打`
          : `想买《${record.postTitle || '未知'}》时拨打`
      }
      
      res.json(Response.success(formattedRecord, '获取详情成功'))
    } else {
      res.status(404).json(Response.error('通话记录不存在', 404))
    }
  } catch (error) {
    console.error('获取通话记录详情失败:', error)
    res.status(500).json(Response.error('获取详情失败: ' + error.message))
  }
}

/**
 * 获取用户通话记录
 */
const getUserCallRecords = async (req, res) => {
  try {
    const { userId } = req.params
    const { page = 1, limit = 10 } = req.query
    
    console.log(`获取用户通话记录: userId=${userId}, page=${page}, limit=${limit}`)
    
    const offset = (parseInt(page) - 1) * parseInt(limit)
    const limitNum = parseInt(limit)
    
    // 获取用户的通话记录
    const query = db.collection('backendCallRecord')
      .where({ userId })
      .orderBy('callTime', 'desc')
    
    // 获取总数
    const countResult = await query.count()
    const total = countResult.total
    
    // 获取分页数据
    const result = await query.skip(offset).limit(limitNum).get()
    
    // 格式化数据
    const formattedData = result.data.map(record => ({
      ...record,
      callTimeFormatted: record.callTime ? new Date(record.callTime).toLocaleString('zh-CN') : '',
      createTimeFormatted: record.createTime ? new Date(record.createTime).toLocaleString('zh-CN') : '',
      postTypeDisplay: record.postType === 'demand' 
        ? `想卖《${record.postTitle || '未知'}》时拨打`
        : `想买《${record.postTitle || '未知'}》时拨打`
    }))
    
    res.json(Response.paginate(formattedData, total, page, limit))
    
  } catch (error) {
    console.error('获取用户通话记录失败:', error)
    res.status(500).json(Response.error('获取用户通话记录失败: ' + error.message))
  }
}

module.exports = {
  getCallRecords,
  getCallRecordsStats,
  getCallRecordDetail,
  getUserCallRecords
}
