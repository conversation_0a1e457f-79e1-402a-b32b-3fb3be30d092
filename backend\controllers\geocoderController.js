const fetch = require('node-fetch');
const crypto = require('crypto');
const Response = require('../utils/response');

// 腾讯地图API配置
const TENCENT_MAP_KEY = '2ALBZ-3NW6T-AU5XI-LX3RS-BGLD5-VMFU3';
const TENCENT_MAP_SK = 'hIzZCfYnczfIcmJQ8OZPAAx3ctCg06to';

/**
 * 生成腾讯地图API签名
 * 根据腾讯地图WebService API官方文档的签名算法
 * 官方文档：https://lbs.qq.com/faq/serverFaq/webServiceKey
 * @param {string} path API路径
 * @param {Object} params 请求参数
 * @returns {string} 签名
 */
const generateTencentMapSignature = (path, params) => {
  // 1. 对参数按key进行升序排列（按参数名升序）
  const sortedKeys = Object.keys(params).sort();
  
  // 2. 拼接参数字符串，参数值不需要URL编码（使用原始数据）
  const paramPairs = [];
  sortedKeys.forEach(key => {
    paramPairs.push(`${key}=${params[key]}`);
  });
  const paramStr = paramPairs.join('&');
  
  // 3. 按照腾讯地图官方文档的签名格式：请求路径 + "?" + 请求参数 + SK
  // 注意：不包含域名，只有路径部分
  const stringToSign = `${path}?${paramStr}${TENCENT_MAP_SK}`;
  
  // 4. 使用MD5进行签名（字符必须为小写）
  const signature = crypto.createHash('md5').update(stringToSign).digest('hex');
  

  
  return signature;
};

/**
 * 从不同格式的geopoint数据中提取经纬度
 */
const extractCoordinates = (geoData) => {
  let latitude, longitude;
  
  // 处理不同的 geopoint 格式
  if (geoData._latitude && geoData._longitude) {
    // Firebase geopoint 格式
    latitude = geoData._latitude;
    longitude = geoData._longitude;
  } else if (geoData.lat && geoData.lng) {
    // 标准 lat/lng 格式
    latitude = geoData.lat;
    longitude = geoData.lng;
  } else if (Array.isArray(geoData) && geoData.length === 2) {
    // 数组格式 [longitude, latitude]
    longitude = geoData[0];
    latitude = geoData[1];
  } else if (geoData.type === 'Point' && Array.isArray(geoData.coordinates) && geoData.coordinates.length === 2) {
    // GeoJSON Point 格式: {type: "Point", coordinates: [longitude, latitude]}
    longitude = geoData.coordinates[0];
    latitude = geoData.coordinates[1];
  } else if (typeof geoData === 'object' && geoData !== null) {
    // 微信小程序 geopoint 格式：包含两个数组的对象
    const keys = Object.keys(geoData);
    
    // 尝试找到包含数值的属性
    let latKey = null, lngKey = null;
    
    for (const key of keys) {
      const value = geoData[key];
      
      if (Array.isArray(value) && value.length > 0 && typeof value[0] === 'number') {
        // 根据数值范围判断是纬度还是经度
        const numValue = value[0];
        if (numValue >= -90 && numValue <= 90 && !latKey) {
          latKey = key;
          latitude = numValue;
        } else if (numValue >= -180 && numValue <= 180 && !lngKey) {
          lngKey = key;
          longitude = numValue;
        }
      } else if (typeof value === 'number') {
        // 直接是数值的情况
        if (value >= -90 && value <= 90 && !latKey) {
          latKey = key;
          latitude = value;
        } else if (value >= -180 && value <= 180 && !lngKey) {
          lngKey = key;
          longitude = value;
        }
      }
    }
    
    // 如果没有找到合适的值，尝试常见的键名
    if (!latKey || !lngKey) {
      for (const key of keys) {
        const value = geoData[key];
        if (key.toLowerCase().includes('lat') || key.toLowerCase().includes('纬')) {
          latitude = Array.isArray(value) ? value[0] : value;
          latKey = key;
        } else if (key.toLowerCase().includes('lng') || key.toLowerCase().includes('lon') || key.toLowerCase().includes('经')) {
          longitude = Array.isArray(value) ? value[0] : value;
          lngKey = key;
        }
      }
    }
    

  }
  
  if (!latitude || !longitude) {
    throw new Error(`无法从geopoint数据中提取经纬度坐标。数据结构: ${JSON.stringify(geoData)}`);
  }
  
  // 验证经纬度范围
  if (latitude < -90 || latitude > 90) {
    throw new Error('纬度范围必须在 -90 到 90 之间');
  }
  
  if (longitude < -180 || longitude > 180) {
    throw new Error('经度范围必须在 -180 到 180 之间');
  }
  
  return { latitude, longitude };
};

/**
 * 逆向地址解析 - 根据经纬度获取地址信息
 */
const reverseGeocode = async (req, res) => {
  try {
    const { geoData } = req.body;
    
    if (!geoData) {
      return res.status(400).json(Response.error('缺少必要参数：geoData', 400));
    }
    
    const coords = extractCoordinates(geoData);



    // 准备API请求参数
    const apiParams = {
      location: `${coords.latitude},${coords.longitude}`,
      key: TENCENT_MAP_KEY,
      get_poi: 1
    };
    
    // 生成签名
    const signature = generateTencentMapSignature('/ws/geocoder/v1/', apiParams);
    
    // 构建完整的API URL
    const apiUrl = `https://apis.map.qq.com/ws/geocoder/v1/?location=${apiParams.location}&key=${apiParams.key}&get_poi=${apiParams.get_poi}&sig=${signature}`;
    

    const response = await fetch(apiUrl);
    
    if (!response.ok) {
      console.error('腾讯地图API请求失败:', response.status, response.statusText);
      return res.status(500).json(Response.error('地址解析服务暂时不可用', 500));
    }

    const data = await response.json();

    if (data.status !== 0) {
      console.error('腾讯地图API返回错误:', data.message);
      return res.status(400).json(Response.error(data.message || '地址解析失败', 400));
    }

    // 整理返回数据
    const result = {
      coordinates: coords,
      address: data.result.address,
      address_component: data.result.address_component,
      pois: data.result.pois || []
    };


    return res.json(Response.success(result, '地址解析成功'));

  } catch (error) {
    console.error('地址解析失败:', error);
    return res.status(500).json(Response.error('地址解析失败: ' + error.message, 500));
  }
};



module.exports = {
  reverseGeocode
};