# 微信小程序后台管理系统 - 后端

## 项目简介

这是一个基于 Node.js + Express + 腾讯云开发的后台管理系统后端，用于管理微信小程序的云数据库数据。

## 技术栈

- **Node.js** - 运行环境
- **Express** - Web框架
- **腾讯云开发 SDK** - 云数据库连接
- **CORS** - 跨域处理
- **dotenv** - 环境变量管理

## 项目结构

```
backend/
├── config/
│   └── database.js          # 数据库配置
├── controllers/
│   ├── userController.js    # 用户控制器
│   └── dashboardController.js # 仪表盘控制器
├── middleware/
│   └── auth.js              # 认证中间件
├── routes/
│   ├── index.js             # 路由汇总
│   ├── users.js             # 用户路由
│   └── dashboard.js         # 仪表盘路由
├── utils/
│   └── response.js          # 响应格式化
├── .env                     # 环境变量配置
├── app.js                   # 主应用文件
└── package.json             # 依赖配置
```

## 环境配置

复制 `.env` 文件并配置以下环境变量：

```env
# 腾讯云开发配置
TCB_SECRET_ID=your_secret_id
TCB_SECRET_KEY=your_secret_key
TCB_ENV_ID=your_env_id

# 服务器配置
PORT=3000
NODE_ENV=development

# 跨域配置
CORS_ORIGIN=http://localhost:5173
```

## 安装和运行

```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev

# 生产模式运行
npm start
```

## API 接口

### 通用接口

- `GET /health` - 健康检查
- `GET /api/test` - 测试接口

### 用户管理

- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/:id` - 更新用户
- `DELETE /api/users/:id` - 删除用户
- `PATCH /api/users/:id/status` - 更新用户状态
- `GET /api/users/stats` - 获取用户统计

### 仪表盘

- `GET /api/dashboard/stats` - 获取统计数据
- `GET /api/dashboard/visit-trend` - 获取访问趋势
- `GET /api/dashboard/user-distribution` - 获取用户分布
- `GET /api/dashboard/monthly-data` - 获取月度数据

## 数据库集合

- `users` - 用户表
- `roles` - 角色表
- `permissions` - 权限表
- `articles` - 文章表
- `categories` - 分类表
- `logs` - 操作日志表

## 响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 部署说明

1. 确保腾讯云开发环境已创建
2. 配置正确的环境变量
3. 运行 `npm install` 安装依赖
4. 运行 `npm start` 启动服务

## 注意事项

- 请妥善保管云开发的 SecretId 和 SecretKey
- 生产环境请设置 NODE_ENV=production
- 建议使用 PM2 等进程管理工具部署