import request from '../utils/request'
import { cacheManager, performanceMonitor, debounce } from '../utils/performance'

// API基础配置
const API_PREFIX = '/api/call-records'
const CACHE_PREFIX = 'call_records_api_'
const DEFAULT_CACHE_EXPIRY = 3 * 60 * 1000 // 3分钟缓存（通话记录更新频率较低）

/**
 * 生成缓存键
 */
const getCacheKey = (method, params = {}) => {
  return `${CACHE_PREFIX}${method}_${JSON.stringify(params)}`
}

/**
 * 清除相关缓存
 */
const clearRelatedCache = (pattern) => {
  const keys = Array.from(cacheManager.cache.keys())
  keys.forEach(key => {
    if (key.includes(pattern)) {
      cacheManager.delete(key)
    }
  })
}

// ===== 企业级API函数 =====

/**
 * 获取通话记录列表（带缓存和优化）
 */
export const getCallRecordsList = async (params = {}, useCache = true) => {
  const cacheKey = getCacheKey('list', params)
  
  // 尝试从缓存获取
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getCallRecordsList')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/list`,
      method: 'get',
      params
    })

    // 缓存结果
    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, DEFAULT_CACHE_EXPIRY)
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getCallRecordsList', params })
    throw error
  }
}

/**
 * 搜索通话记录（防抖处理）
 */
export const searchCallRecords = debounce(async (searchParams) => {
  return getCallRecordsList(searchParams, false) // 搜索不使用缓存
}, 300)

/**
 * 获取通话记录详情
 */
export const getCallRecordDetail = async (id, useCache = true) => {
  const cacheKey = getCacheKey('detail', { id })
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getCallRecordDetail')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/detail/${id}`,
      method: 'get'
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, DEFAULT_CACHE_EXPIRY)
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getCallRecordDetail', id })
    throw error
  }
}

/**
 * 获取通话记录统计数据
 */
export const getCallRecordsStats = async (useCache = true) => {
  const cacheKey = getCacheKey('stats')
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getCallRecordsStats')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/stats`,
      method: 'get'
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, 2 * 60 * 1000) // 统计数据缓存2分钟
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getCallRecordsStats' })
    throw error
  }
}

/**
 * 获取用户通话记录
 */
export const getUserCallRecords = async (userId, params = {}, useCache = true) => {
  const cacheKey = getCacheKey('user_records', { userId, ...params })
  
  if (useCache) {
    const cached = cacheManager.get(cacheKey)
    if (cached) {
      return cached
    }
  }

  const timer = performanceMonitor.startTimer('getUserCallRecords')
  
  try {
    const response = await request({
      url: `${API_PREFIX}/user/${userId}`,
      method: 'get',
      params
    })

    if (useCache && response.data) {
      cacheManager.set(cacheKey, response, DEFAULT_CACHE_EXPIRY)
    }

    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'getUserCallRecords', userId, params })
    throw error
  }
}

/**
 * 批量获取通话记录（用于大数据量场景）
 */
export const batchGetCallRecords = async (batchParams, useCache = false) => {
  const timer = performanceMonitor.startTimer('batchGetCallRecords')
  
  try {
    const promises = batchParams.map(params => 
      getCallRecordsList(params, useCache)
    )
    
    const results = await Promise.allSettled(promises)
    
    timer.end()
    return results.map(result => 
      result.status === 'fulfilled' ? result.value : null
    ).filter(Boolean)
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'batchGetCallRecords', batchParams })
    throw error
  }
}

/**
 * 预加载通话记录数据（用于性能优化）
 */
export const preloadCallRecordsData = async (preloadParams = []) => {
  const timer = performanceMonitor.startTimer('preloadCallRecordsData')
  
  try {
    // 预加载统计数据
    getCallRecordsStats(true)
    
    // 预加载指定参数的列表数据
    preloadParams.forEach(params => {
      getCallRecordsList(params, true)
    })
    
    timer.end()
    console.log('通话记录数据预加载完成')
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'preloadCallRecordsData', preloadParams })
    console.warn('通话记录数据预加载失败:', error.message)
  }
}

/**
 * 清除通话记录相关缓存
 */
export const clearCallRecordsCache = () => {
  clearRelatedCache(CACHE_PREFIX)
  console.log('通话记录缓存已清除')
}

/**
 * 获取缓存状态信息
 */
export const getCallRecordsCacheStatus = () => {
  const allKeys = Array.from(cacheManager.cache.keys())
  const callRecordsKeys = allKeys.filter(key => key.startsWith(CACHE_PREFIX))
  
  return {
    totalCached: callRecordsKeys.length,
    keys: callRecordsKeys,
    size: callRecordsKeys.reduce((size, key) => {
      const value = cacheManager.get(key)
      return size + (value ? JSON.stringify(value).length : 0)
    }, 0)
  }
}

/**
 * 导出数据（用于报表功能）
 */
export const exportCallRecordsData = async (exportParams = {}) => {
  const timer = performanceMonitor.startTimer('exportCallRecordsData')
  
  try {
    // 获取所有数据（不分页）
    const allDataParams = {
      ...exportParams,
      page: 1,
      limit: 10000 // 大数量限制
    }
    
    const response = await getCallRecordsList(allDataParams, false)
    
    timer.end()
    return response
    
  } catch (error) {
    timer.end()
    performanceMonitor.recordError(error, { api: 'exportCallRecordsData', exportParams })
    throw error
  }
}

// ===== 兼容性API函数（保持向后兼容） =====

/**
 * 获取通话记录列表（简化版本）
 */
export const getCallRecords = (params) => {
  return getCallRecordsList(params)
}

/**
 * 获取通话记录统计（简化版本）
 */
export const getCallStats = () => {
  return getCallRecordsStats()
}
