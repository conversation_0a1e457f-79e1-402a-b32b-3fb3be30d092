const express = require('express')
const router = express.Router()
const userController = require('../controllers/userController')
const { authMiddleware, checkPermission } = require('../middleware/auth')

// 不需要认证的路由 - 查询发布人电话（管理员功能）
router.get('/phone/:openid', userController.getUserPhoneByOpenid)

// 应用认证中间件
router.use(authMiddleware)

// 用户CRUD路由
router.get('/', checkPermission('read'), userController.getUsers)
router.post('/', checkPermission('create'), userController.createUser)
router.put('/:id', checkPermission('update'), userController.updateUser)
router.delete('/:id', checkPermission('delete'), userController.deleteUser)
router.patch('/:id/status', checkPermission('update'), userController.updateUserStatus)

// 用户统计
router.get('/stats', checkPermission('read'), userController.getUserStats)

module.exports = router