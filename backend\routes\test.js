const express = require('express')
const router = express.Router()
const { db } = require('../config/database')

// 测试云数据库连接 - 只读取users集合
router.get('/db-test', async (req, res) => {
  try {
    console.log('正在测试云数据库连接...')
    
    // 读取users集合的数据
    const result = await db.collection('users').limit(10).get()
    
    res.json({
      code: 200,
      message: '云数据库连接成功',
      data: {
        connected: true,
        collectionName: 'users',
        totalCount: result.data.length,
        users: result.data
      }
    })
  } catch (error) {
    console.error('云数据库连接失败:', error)
    res.status(500).json({
      code: 500,
      message: '云数据库连接失败',
      error: error.message,
      data: {
        connected: false
      }
    })
  }
})

// 获取用户列表 - 只读取，不修改
router.get('/users', async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query
    const skip = (page - 1) * limit
    
    // 读取users集合数据
    const result = await db.collection('users')
      .skip(skip)
      .limit(parseInt(limit))
      .get()
    
    res.json({
      code: 200,
      message: '获取用户列表成功',
      data: {
        users: result.data,
        total: result.data.length,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户列表失败',
      error: error.message
    })
  }
})

module.exports = router 