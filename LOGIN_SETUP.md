# 登录认证系统设置指南

## 🎯 功能概述

已为您的Vue 3 + Element Plus项目实现了完整的登录认证系统，包括：

- ✅ 专业美观的登录页面
- ✅ JWT token认证机制
- ✅ 路由守卫保护
- ✅ 自动登录状态管理
- ✅ 登出功能
- ✅ 401错误自动处理

## 🗄️ 数据库设置

### 1. 创建admin集合

在腾讯云开发控制台中创建名为 `admin` 的集合，字段结构如下：

```json
{
  "account": "admin",           // 管理员账号
  "pw": "加密后的密码",         // 使用bcrypt加密
  "name": "管理员",             // 显示名称
  "createTime": "2024-01-01T00:00:00.000Z",
  "updateTime": "2024-01-01T00:00:00.000Z"
}
```

### 2. 生成加密密码

在后端目录运行以下命令生成加密密码：

```bash
cd backend
node scripts/hashPassword.js your_password
```

这将输出加密后的密码和完整的数据库文档结构。

### 3. 在数据库中添加管理员

将生成的JSON文档添加到腾讯云开发的admin集合中。

## ⚙️ 环境配置

### 1. 后端环境变量

复制 `backend/.env.example` 为 `backend/.env` 并配置：

```env
# 腾讯云开发配置
TCB_SECRET_ID=your_secret_id
TCB_SECRET_KEY=your_secret_key
TCB_ENV_ID=your_env_id

# 服务器配置
PORT=3000
NODE_ENV=development

# JWT配置（重要：生产环境请更换为复杂密钥）
JWT_SECRET=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_EXPIRES_IN=24h
```

## 🚀 启动项目

### 1. 启动后端服务

```bash
cd backend
npm run dev
```

### 2. 启动前端服务

```bash
npm run dev
```

## 🔐 使用说明

### 登录流程

1. 访问任何受保护的页面会自动重定向到 `/login`
2. 输入管理员账号和密码
3. 登录成功后自动跳转到原始页面或仪表盘
4. 支持"记住我"功能，延长登录有效期

### 受保护的页面

- `/dashboard` - 仪表盘
- `/search-words` - 增加检索词
- `/supply-list` - 供应列表

### 登出功能

点击右上角用户头像 → 退出登录

## 🛡️ 安全特性

- **JWT Token认证**：安全的无状态认证
- **密码加密**：使用bcrypt进行密码哈希
- **自动登出**：token过期或无效时自动清除登录状态
- **路由保护**：未登录用户无法访问受保护页面
- **记住我功能**：可选择延长登录有效期

## 🎨 UI特性

- **响应式设计**：支持桌面和移动端
- **现代化界面**：渐变背景、毛玻璃效果、动画过渡
- **表单验证**：实时验证用户输入
- **加载状态**：登录过程中的加载提示
- **错误处理**：友好的错误提示信息

## 🔧 技术实现

### 前端技术栈
- Vue 3 Composition API
- Element Plus UI组件
- Pinia状态管理
- Vue Router导航守卫
- Axios请求拦截

### 后端技术栈
- Node.js + Express
- JWT token生成和验证
- bcryptjs密码加密
- 腾讯云开发SDK

## 📝 注意事项

1. **生产环境安全**：
   - 更换JWT_SECRET为复杂密钥
   - 设置NODE_ENV=production
   - 使用HTTPS协议

2. **密码安全**：
   - 管理员密码建议使用强密码
   - 定期更换密码

3. **Token管理**：
   - 默认token有效期24小时
   - "记住我"功能延长至7天
   - token存储在localStorage中

## 🐛 故障排除

### 常见问题

1. **登录失败**：检查数据库中的账号密码是否正确
2. **Token验证失败**：检查JWT_SECRET配置是否一致
3. **跨域问题**：确认后端CORS配置包含前端地址
4. **数据库连接失败**：检查腾讯云开发配置是否正确

