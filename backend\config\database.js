const tcb = require('@cloudbase/node-sdk')
const path = require('path')
const fs = require('fs')

// 加载环境变量
const envPath = path.join(__dirname, '..', '.env')
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8')
  const envLines = envContent.split('\n')
  
  envLines.forEach(line => {
    const trimmedLine = line.trim()
    if (trimmedLine && !trimmedLine.startsWith('#')) {
      const equalIndex = trimmedLine.indexOf('=')
      if (equalIndex > 0) {
        const key = trimmedLine.substring(0, equalIndex).trim()
        const value = trimmedLine.substring(equalIndex + 1).trim()
        process.env[key] = value
      }
    }
  })
}

console.log('环境变量检查:', {
  secretId: process.env.TCB_SECRET_ID ? '已设置' : '未设置',
  secretKey: process.env.TCB_SECRET_KEY ? '已设置' : '未设置',
  envId: process.env.TCB_ENV_ID || '未设置'
})

// 初始化云开发 - 优化配置
const app = tcb.init({
  secretId: process.env.TCB_SECRET_ID,
  secretKey: process.env.TCB_SECRET_KEY,
  env: process.env.TCB_ENV_ID,
  timeout: 20000, // 增加超时时间到20秒
  retry: 3, // 重试次数
  retryDelay: 1000 // 重试延迟1秒
})

// 获取数据库引用
const db = app.database()

// 测试数据库连接 - 增加重试机制
const testConnection = async (retryCount = 0) => {
  try {
    console.log(`正在测试云数据库连接... (第${retryCount + 1}次尝试)`)
    
    // 使用更简单的查询来测试连接
    const result = await Promise.race([
      db.collection('users').limit(1).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 10000)
      )
    ])
    
    console.log('✅ 云数据库连接成功!')
    console.log('📊 users集合数据条数:', result.data.length)
    if (result.data.length > 0) {
      console.log('📋 示例数据:', result.data[0])
    }
    return true
  } catch (error) {
    console.error(`❌ 云数据库连接失败 (第${retryCount + 1}次):`, error.message)
    
    // 如果还有重试次数，则重试
    if (retryCount < 2) {
      console.log(`🔄 ${2 - retryCount}秒后重试...`)
      await new Promise(resolve => setTimeout(resolve, 2000))
      return testConnection(retryCount + 1)
    }
    
    return false
  }
}

// 数据库集合名称
const COLLECTIONS = {
  USERS: 'users',           // 用户表
  ROLES: 'roles',           // 角色表
  PERMISSIONS: 'permissions', // 权限表
  ARTICLES: 'articles',     // 文章表
  CATEGORIES: 'categories', // 分类表
  LOGS: 'logs'             // 操作日志表
}

module.exports = {
  db,
  COLLECTIONS,
  app,
  testConnection
}