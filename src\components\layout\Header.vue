<template>
  <el-header class="layout-header" :class="{ 'is-mobile': isMobile }">
    <div class="header-left">
      <!-- 折叠按钮 -->
      <el-button
        type="text"
        size="large"
        @click="toggleCollapse"
        class="collapse-btn"
        :class="{ 'mobile-menu-btn': isMobile }"
      >
        <el-icon size="20">
          <Expand v-if="isCollapse" />
          <Fold v-else />
        </el-icon>
      </el-button>

      <!-- 面包屑导航 - 移动端隐藏 -->
      <el-breadcrumb 
        v-if="!isMobile" 
        separator="/" 
        class="breadcrumb"
      >
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>{{ currentRouteTitle }}</el-breadcrumb-item>
      </el-breadcrumb>
      
      <!-- 移动端页面标题 -->
      <h3 v-if="isMobile" class="mobile-title">{{ currentRouteTitle }}</h3>
    </div>

    <div class="header-right">
      <!-- 用户信息下拉菜单 -->
      <el-dropdown trigger="click" class="user-dropdown">
        <div class="user-info" :class="{ 'mobile-user-info': isMobile }">
          <el-avatar :src="userInfo.avatar" :size="isMobile ? 28 : 32" />
          <span v-if="!isMobile" class="username">{{ userInfo.name }}</span>
          <el-icon v-if="!isMobile" class="dropdown-icon">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- 移动端显示用户名 -->
            <el-dropdown-item v-if="isMobile" disabled>
              <strong>{{ userInfo.name }}</strong>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item>
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided @click="handleLogout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useLayoutStore, useUserStore } from '../../stores'
import { ElMessage, ElMessageBox } from 'element-plus'

// 接收props
const props = defineProps({
  isMobile: {
    type: Boolean,
    default: false
  }
})

const route = useRoute()
const layoutStore = useLayoutStore()
const userStore = useUserStore()

// 计算属性
const isCollapse = computed(() => layoutStore.isCollapse)
const userInfo = computed(() => userStore.userInfo)
const currentRouteTitle = computed(() => route.meta?.title || '未知页面')
const isMobile = computed(() => props.isMobile)

// 方法
const toggleCollapse = () => {
  layoutStore.toggleCollapse()
}

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用store的登出方法
    userStore.logout()
  } catch {
    // 用户取消
  }
}
</script>

<style scoped>
.layout-header {
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  margin-right: 16px;
  color: #666;
}

.collapse-btn:hover {
  color: #409EFF;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.username {
  font-size: 14px;
  color: #333;
  margin-left: 4px;
}

.dropdown-icon {
  font-size: 12px;
  color: #999;
}



/* 移动端样式 */
.mobile-menu-btn {
  background: #f5f5f5 !important;
  border-radius: 8px !important;
  padding: 8px !important;
}

.mobile-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 0 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.mobile-user-info {
  padding: 6px 8px !important;
  gap: 0 !important;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px !important;
    height: 56px !important;
  }
  
  .header-left {
    flex: 1;
    min-width: 0;
  }
  
  .header-right {
    gap: 8px !important;
  }
  
  .collapse-btn {
    margin-right: 8px !important;
  }
}

@media (max-width: 480px) {
  .layout-header {
    padding: 0 12px !important;
    height: 52px !important;
  }
  
  .mobile-title {
    font-size: 16px;
    max-width: 150px;
  }
  
  .header-right {
    gap: 4px !important;
  }
  
  .user-info {
    padding: 4px 6px !important;
  }
}


</style>