const express = require('express')
const router = express.Router()
const { app } = require('../config/database')

// 处理云存储图片访问
router.get('/cloud-image', async (req, res) => {
  try {
    // 检查云开发实例是否可用
    if (!app) {
      return res.status(500).json({
        code: 500,
        message: '云存储服务不可用'
      })
    }

    const { path, filename } = req.query

    if (!path && !filename) {
      return res.status(400).json({
        code: 400,
        message: '缺少图片路径或文件名参数'
      })
    }

    let cloudPath = path

    // 如果提供的是文件名，构建完整的云存储路径
    if (filename && !path) {
      const cloudPrefix = process.env.TCB_STORAGE_PREFIX || 'cloud://miaomuzhongxin-0giu90bpa4cbeaf5.6d69-miaomuzhongxin-0giu90bpa4cbeaf5-1330490a9_f51ab3f'
      cloudPath = `${cloudPrefix}/${filename}`
    }

    // 检查是否是云存储路径
    if (!cloudPath || !cloudPath.startsWith('cloud://')) {
      return res.status(400).json({
        code: 400,
        message: '无效的云存储路径',
        providedPath: cloudPath
      })
    }

    try {

      // 使用 getTempFileURL 获取临时访问链接
      const tempUrlResult = await app.getTempFileURL({
        fileList: [cloudPath]
      })

      if (tempUrlResult && tempUrlResult.fileList && tempUrlResult.fileList.length > 0) {
        const fileInfo = tempUrlResult.fileList[0]

        // 检查返回的临时链接
        if (fileInfo.tempFileURL) {
          return res.redirect(fileInfo.tempFileURL)
        } else {
          return res.status(404).json({
            code: 404,
            message: '图片不存在或无法访问',
            error: fileInfo.code || 'UNKNOWN_ERROR'
          })
        }
      } else {
        return res.status(500).json({
          code: 500,
          message: '云存储API返回数据异常'
        })
      }

    } catch (error) {
      res.status(500).json({
        code: 500,
        message: '云存储访问失败',
        error: error.message
      })
    }
  } catch (error) {
    res.status(500).json({
      code: 500,
      message: '服务器内部错误',
      error: error.message
    })
  }
})

module.exports = router
