const jwt = require('jsonwebtoken')
const { db } = require('../config/database')

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'

// 身份验证中间件
const authMiddleware = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1]

    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '未提供访问令牌',
        data: null
      })
    }

    // 验证JWT token
    const decoded = jwt.verify(token, JWT_SECRET)

    // 查询管理员是否仍然存在
    const adminCollection = db.collection('admin')
    const adminDoc = await adminCollection.doc(decoded.id).get()

    if (!adminDoc.data) {
      return res.status(401).json({
        code: 401,
        message: '用户不存在',
        data: null
      })
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: decoded.id,
      account: decoded.account,
      role: decoded.role
    }

    next()
  } catch (error) {
    console.error('Token验证错误:', error)

    let message = '令牌验证失败'
    if (error.name === 'JsonWebTokenError') {
      message = '无效的访问令牌'
    } else if (error.name === 'TokenExpiredError') {
      message = '访问令牌已过期'
    }

    res.status(401).json({
      code: 401,
      message,
      data: null
    })
  }
}

// 权限检查中间件
const checkPermission = (permission) => {
  return (req, res, next) => {
    const userRole = req.user?.role
    
    // 超级管理员拥有所有权限
    if (userRole === 'admin') {
      return next()
    }
    
    // 这里可以实现具体的权限检查逻辑
    // 暂时简单检查
    if (permission === 'read' || userRole === 'manager') {
      return next()
    }
    
    res.status(403).json({
      code: 403,
      message: '权限不足',
      data: null
    })
  }
}

module.exports = {
  authMiddleware,
  checkPermission
}