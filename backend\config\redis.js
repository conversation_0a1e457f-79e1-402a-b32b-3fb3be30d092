const Redis = require('ioredis')

// Redis配置
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxRetriesPerRequest: null,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
  retryDelayOnClusterDown: 300,
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3
}

// 创建Redis实例
const redis = new Redis(redisConfig)

// 连接事件监听
redis.on('connect', () => {
  console.log('✅ Redis连接成功')
})

redis.on('ready', () => {
  console.log('🚀 Redis准备就绪')
})

redis.on('error', (err) => {
  console.error('❌ Redis连接错误:', err.message)
})

redis.on('close', () => {
  console.log('🔌 Redis连接已关闭')
})

redis.on('reconnecting', () => {
  console.log('🔄 Redis重新连接中...')
})

// 测试Redis连接
const testRedisConnection = async () => {
  try {
    await redis.ping()
    console.log('🎯 Redis连接测试成功')
    return true
  } catch (error) {
    console.error('❌ Redis连接测试失败:', error.message)
    return false
  }
}

module.exports = {
  redis,
  testRedisConnection
}
