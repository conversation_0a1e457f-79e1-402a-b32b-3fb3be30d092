:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  /* 移除默认样式，避免影响布局 */
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* ===== 全局响应式样式 ===== */

/* 基础响应式设置 */
* {
  box-sizing: border-box;
}

html {
  /* 防止移动端横向滚动 */
  overflow-x: hidden;
}

body {
  /* 防止移动端橡皮筋效果 */
  overscroll-behavior: none;
  /* 优化移动端触摸滚动 */
  -webkit-overflow-scrolling: touch;
}

/* Element Plus 全局响应式优化 */

/* 移动端对话框适配 */
@media (max-width: 768px) {
  .el-dialog {
    width: 95% !important;
    margin: 5vh auto !important;
  }
  
  .el-dialog__header {
    padding: 16px !important;
  }
  
  .el-dialog__body {
    padding: 16px !important;
  }
  
  .el-dialog__footer {
    padding: 12px 16px !important;
  }
  
  /* 移动端消息提示适配 */
  .el-message {
    min-width: 280px !important;
    margin: 0 16px !important;
  }
  
  /* 移动端通知适配 */
  .el-notification {
    width: calc(100vw - 32px) !important;
    margin: 0 16px !important;
  }
  
  /* 移动端选择器适配 */
  .el-select-dropdown {
    max-width: calc(100vw - 32px) !important;
  }
  
  /* 移动端日期选择器适配 */
  .el-date-picker {
    width: calc(100vw - 32px) !important;
  }
  
  /* 移动端抽屉适配 */
  .el-drawer {
    width: 90% !important;
  }
  
  /* 移动端表单适配 */
  .el-form-item__label {
    font-size: 14px !important;
  }
  
  .el-input__inner {
    font-size: 14px !important;
  }
  
  .el-textarea__inner {
    font-size: 14px !important;
  }
  
  /* 移动端按钮适配 */
  .el-button {
    min-height: 36px !important;
  }
  
  .el-button--large {
    min-height: 40px !important;
  }
  
  .el-button--small {
    min-height: 28px !important;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .el-dialog {
    width: 98% !important;
    margin: 2vh auto !important;
  }
  
  .el-dialog__header {
    padding: 12px !important;
  }
  
  .el-dialog__body {
    padding: 12px !important;
  }
  
  .el-dialog__footer {
    padding: 8px 12px !important;
  }
  
  .el-message {
    min-width: 260px !important;
    margin: 0 12px !important;
  }
  
  .el-notification {
    width: calc(100vw - 24px) !important;
    margin: 0 12px !important;
  }
  
  .el-drawer {
    width: 95% !important;
  }
  
  .el-form-item__label {
    font-size: 13px !important;
  }
  
  .el-input__inner {
    font-size: 13px !important;
  }
  
  .el-textarea__inner {
    font-size: 13px !important;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .el-dialog {
    width: 80% !important;
  }
  
  .el-drawer {
    width: 60% !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 增加可点击区域 */
  .el-button,
  .el-input,
  .el-select,
  .el-switch {
    min-height: 44px !important;
  }
  
  /* 优化表格行高 */
  .el-table td,
  .el-table th {
    min-height: 44px !important;
  }
  
  /* 优化菜单项高度 */
  .el-menu-item {
    min-height: 48px !important;
  }
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
