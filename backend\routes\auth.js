const express = require('express')
const router = express.Router()
const authController = require('../controllers/authController')
const { authMiddleware } = require('../middleware/auth')

// 公开路由（不需要认证）
router.post('/login', authController.login)
router.get('/verify', authController.verifyToken)

// 需要认证的路由
router.use(authMiddleware)
router.post('/change-password', authController.changePassword)

module.exports = router
