const express = require('express')
const cors = require('cors')
require('dotenv').config()

const routes = require('./routes')
const { testConnection } = require('./config/database')

const app = express()
const PORT = process.env.PORT || 3000

// 中间件配置 - 动态CORS配置
app.use(cors({
  origin: function (origin, callback) {
    // 允许没有origin的请求（如移动应用、Postman等）
    if (!origin) return callback(null, true)

    // 允许localhost和127.0.0.1的所有端口
    if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
      return callback(null, true)
    }

    // 允许局域网IP地址（192.168.x.x, 10.x.x.x, 172.16-31.x.x）的5173和5174端口
    const ipRegex = /^https?:\/\/(192\.168\.\d+\.\d+|10\.\d+\.\d+\.\d+|172\.(1[6-9]|2\d|3[01])\.\d+\.\d+):(5173|5174)$/
    if (ipRegex.test(origin)) {
      return callback(null, true)
    }

    // 开发环境允许所有来源
    if (process.env.NODE_ENV !== 'production') {
      return callback(null, true)
    }

    // 生产环境拒绝未知来源
    callback(new Error('Not allowed by CORS'))
  },
  credentials: true
}))

app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`)
  next()
})

// 注册路由
app.use('/', routes)

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    data: null
  })
})

// 全局错误处理
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    data: null
  })
})

// 启动服务器
app.listen(PORT, '0.0.0.0', async () => {
  console.log(`🚀 服务器启动成功！`)
  console.log(`📍 本地地址: http://localhost:${PORT}`)

  console.log(`🌍 环境: ${process.env.NODE_ENV || 'development'}`)
  console.log(`☁️ 云开发环境: ${process.env.TCB_ENV_ID}`)
  
  // 测试数据库连接
  try {
    await testConnection()
  } catch (error) {
    console.error('数据库连接测试失败:', error.message)
  }
})

module.exports = app