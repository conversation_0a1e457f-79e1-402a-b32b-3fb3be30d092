import request from '../utils/request'

// 获取仪表盘统计数据
export const getDashboardStats = () => {
  return request.get('/api/dashboard/stats')
}

// 获取所有用户数据 - 支持分页和搜索
export const getAllUsers = (params = {}) => {
  // 确保参数格式与后端一致
  const queryParams = {
    page: params.page || 1,
    pageSize: params.pageSize || 20,
    keyword: params.keyword || '',
    searchType: params.searchType || 'nickName'
  }

  return request.get('/api/dashboard/users', { params: queryParams })
}

// 更新用户信息
export const updateUser = (userData) => {
  return request.put(`/api/dashboard/users/${userData._id}`, userData)
}

// 获取单个用户详情
export const getUserById = (userId) => {
  return request.get(`/api/dashboard/users/${userId}`)
}

// ===== 积分管理相关API =====

// 搜索用户（用于积分管理）
export const searchUserForPoints = (userId) => {
  return request.get('/api/dashboard/points/search-user', {
    params: { userId }
  })
}

// 个人积分操作
export const updateUserPoints = (data) => {
  return request.post('/api/dashboard/points/update-user', data)
}

// 批量积分操作（使用更长的超时时间）
export const batchUpdatePoints = (data) => {
  return request.post('/api/dashboard/points/batch-update', data, {
    timeout: 120000 // 2分钟超时
  })
}

// 批量积分操作（支持取消和进度）
export const batchUpdatePointsWithProgress = (data, signal) => {
  return request.post('/api/dashboard/points/batch-update', data, {
    signal, // 支持取消请求
    timeout: 0 // 不设置超时，由用户手动取消
  })
}