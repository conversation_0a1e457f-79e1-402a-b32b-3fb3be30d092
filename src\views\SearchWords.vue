<template>
  <div class="search-words-container">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h3>增加检索词管理</h3>
          <p class="description">从notice和searchWord集合中管理植物名称，并添加到检索列表</p>
        </div>
      </template>

      <!-- 数据源选择 -->
      <div class="source-selector">
        <el-radio-group v-model="selectedSource" @change="handleSourceChange">
          <el-radio value="notice">Notice通知集合</el-radio>
          <el-radio value="searchWord">SearchWord备份集合</el-radio>
        </el-radio-group>


      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        class="compact-table"
        max-height="600"
      >
        <el-table-column prop="plantName" label="植物名称（双击编辑）" width="250">
          <template #default="{ row, $index }">
            <el-input
              v-if="editingIndex === $index"
              v-model="editingValue"
              @blur="handleSaveEdit(row, $index)"
              @keyup.enter="handleSaveEdit(row, $index)"
              @keyup.esc="handleCancelEdit"
              size="small"
              placeholder="请输入植物名称"
              ref="editInput"
            />
            <span
              v-else
              class="plant-name-display"
              @dblclick="handleEdit($index, row.plantName)"
              :title="'双击编辑植物名称'"
            >
              {{ row.plantName || '暂无（双击编辑）' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="130">
          <template #default="{ row }">
            <el-button
              type="success"
              size="small"
              :disabled="!row.plantName"
              @click="handleAddToPlantsList(row)"
            >
              添加到检索
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[40, 80, 120, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 检索列表管理 -->
    <el-card class="page-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <h3>检索词列表 (plants_list)</h3>
          <p class="description">当前检索词库中的所有词条</p>
        </div>
      </template>

      <el-table
        v-loading="plantsListLoading"
        :data="plantsListData"
        stripe
        class="compact-table"
        max-height="400"
      >
        <el-table-column prop="name" label="检索名称" width="200" />
        <el-table-column prop="hit" label="命中次数" width="100" />
        <el-table-column label="操作" width="80">
          <template #default="{ row }">
            <el-popconfirm
              title="确定删除这个检索词吗？"
              @confirm="handleDeletePlantItem(row._id)"
            >
              <template #reference>
                <el-button type="danger" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 检索列表分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="plantsCurrentPage"
          v-model:page-size="plantsPageSize"
          :page-sizes="[40, 80, 120, 200]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="plantsTotal"
          @size-change="handlePlantsListSizeChange"
          @current-change="handlePlantsListCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { getSourceData, updatePlantName, addToPlantsList, getPlantsList, deletePlantItem } from '../api/searchWords'

// 响应式数据
const selectedSource = ref('notice')
const tableData = ref([])
const loading = ref(false)
const editingIndex = ref(-1)
const editingValue = ref('')

// 分页数据
const currentPage = ref(1)
const pageSize = ref(80)
const total = ref(0)

// 检索列表数据
const plantsListData = ref([])
const plantsListLoading = ref(false)
const plantsCurrentPage = ref(1)
const plantsPageSize = ref(80)
const plantsTotal = ref(0)



// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const response = await getSourceData(selectedSource.value, {
      page: currentPage.value,
      limit: pageSize.value
    })

    if (response.code === 200) {
      tableData.value = response.data.data || []
      total.value = response.data.total || 0

      console.log('数据加载结果:', {
        当前页数据量: tableData.value.length,
        总数: total.value,
        当前页: currentPage.value,
        每页大小: pageSize.value
      })
    } else {
      ElMessage.error(response.message || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载检索列表数据
const loadPlantsList = async () => {
  plantsListLoading.value = true
  try {
    const response = await getPlantsList({
      page: plantsCurrentPage.value,
      limit: plantsPageSize.value
    })
    
    if (response.code === 200) {
      plantsListData.value = response.data.data || []
      plantsTotal.value = response.data.total || 0
    } else {
      ElMessage.error(response.message || '加载检索列表失败')
    }
  } catch (error) {
    console.error('加载检索列表失败:', error)
    ElMessage.error('加载检索列表失败')
  } finally {
    plantsListLoading.value = false
  }
}

// 数据源切换
const handleSourceChange = async () => {
  currentPage.value = 1
  loadData()
}

// 编辑植物名称
const handleEdit = async (index, value) => {
  editingIndex.value = index
  editingValue.value = value || ''

  // 自动聚焦到输入框
  await nextTick()
  const editInput = document.querySelector('.el-input__inner')
  if (editInput) {
    editInput.focus()
    editInput.select()
  }
}

// 取消编辑
const handleCancelEdit = () => {
  editingIndex.value = -1
  editingValue.value = ''
}

// 保存编辑
const handleSaveEdit = async (row, index) => {
  if (!editingValue.value.trim()) {
    ElMessage.warning('植物名称不能为空')
    return
  }

  try {
    const response = await updatePlantName(selectedSource.value, row._id, editingValue.value)

    if (response.code === 200) {
      // 更新本地数据
      tableData.value[index].plantName = editingValue.value
      ElMessage.success('修改成功')
      handleCancelEdit()
    } else {
      ElMessage.error(response.message || '修改失败')
    }
  } catch (error) {
    console.error('修改失败:', error)

    // 即使出现错误，也重新加载数据来验证是否实际更新成功
    console.log('重新加载数据以验证更新状态...')
    await loadData()

    // 检查数据是否实际更新了
    const updatedRow = tableData.value[index]
    if (updatedRow && updatedRow.plantName === editingValue.value.trim()) {
      ElMessage.success('修改成功（数据已更新）')
      handleCancelEdit()
    } else {
      ElMessage.error('修改失败')
    }
  }
}





// 添加到检索列表
const handleAddToPlantsList = async (row) => {
  if (!row.plantName) {
    ElMessage.warning('植物名称为空，无法添加')
    return
  }

  try {
    const response = await addToPlantsList(row.plantName)

    if (response.code === 200) {
      ElMessage.success('已添加到检索列表')

      // 刷新检索列表
      loadPlantsList()
    } else {
      ElMessage.error(response.message || '添加失败')
    }
  } catch (error) {
    console.error('添加失败:', error)
    ElMessage.error('添加失败')
  }
}

// 删除检索词
const handleDeletePlantItem = async (id) => {
  try {
    const response = await deletePlantItem(id)

    if (response.code === 200) {
      ElMessage.success('删除成功')
      loadPlantsList()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    console.error('删除失败:', error)

    // 即使出现错误，也重新加载数据来验证是否实际删除成功
    console.log('重新加载数据以验证删除状态...')
    const originalTotal = plantsTotal.value
    await loadPlantsList()

    // 检查数据是否实际被删除了（总数减少）
    if (plantsTotal.value < originalTotal) {
      ElMessage.success('删除成功（数据已删除）')
    } else {
      ElMessage.error('删除失败')
    }
  }
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  loadData()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  loadData()
}

// 检索列表分页处理
const handlePlantsListSizeChange = (val) => {
  plantsPageSize.value = val
  plantsCurrentPage.value = 1
  loadPlantsList()
}

const handlePlantsListCurrentChange = (val) => {
  plantsCurrentPage.value = val
  loadPlantsList()
}

// 组件挂载时加载数据
onMounted(async () => {
  loadData()
  loadPlantsList()
})
</script>

<style scoped>
.search-words-container {
  padding: 0;
}

/* 紧凑表格样式 */
.compact-table {
  max-width: 600px;
  margin: 0 auto;
  width: auto !important;
}

/* 表格容器居中 */
.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 调整页面最大宽度 */
.search-words-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 分页组件居中 */
.el-pagination {
  justify-content: center;
  margin-top: 20px;
}

/* 表格字体大小优化 */
.el-table {
  font-size: 14px;
}

.el-table .cell {
  font-size: 14px;
  line-height: 1.5;
  padding: 10px 8px;
}

.el-table th .cell {
  font-size: 14px;
  font-weight: 600;
  padding: 10px 8px;
}

.page-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  flex-direction: column;
}

.card-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 18px;
  font-weight: 600;
}

.description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.source-selector {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.filter-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-status .el-tag {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-words-container {
    max-width: 100%;
    padding: 0 10px;
  }

  .compact-table {
    max-width: 100%;
  }

  .source-selector {
    max-width: 100%;
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .filter-status {
    justify-content: center;
  }

  /* 移动端表格列宽调整 */
  .el-table .el-table__cell {
    padding: 8px 4px;
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.el-input {
  max-width: 200px;
}

/* 植物名称显示样式 */
.plant-name-display {
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: inline-block;
  min-width: 100px;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
}

.plant-name-display:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

/* 输入框字体大小 */
.el-input__inner {
  font-size: 14px;
  padding: 6px 10px;
}

/* 按钮字体大小 */
.el-button {
  font-size: 13px;
  padding: 6px 12px;
}

.el-button--small {
  font-size: 13px;
  padding: 5px 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-words-container {
    padding: 0;
  }
  
  .source-selector {
    padding: 12px;
  }
  
  .card-header h3 {
    font-size: 16px;
  }
  
  .description {
    font-size: 13px;
  }
}




</style>