const express = require('express')
const router = express.Router()
const callRecordsController = require('../controllers/callRecordsController')

// 获取通话记录列表
router.get('/list', callRecordsController.getCallRecords)

// 获取通话记录统计数据
router.get('/stats', callRecordsController.getCallRecordsStats)

// 获取通话记录详情
router.get('/detail/:id', callRecordsController.getCallRecordDetail)

// 获取用户通话记录
router.get('/user/:userId', callRecordsController.getUserCallRecords)

module.exports = router
