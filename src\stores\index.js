import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import router from '../router'
import request from '../utils/request'
import { resetTokenExpiredState } from '../utils/auth'

// 用户信息store
export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('token') || '')
  const userInfo = ref({
    account: '',
    name: '管理员',
    avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
    role: 'admin'
  })
  const isLoggedIn = ref(!!localStorage.getItem('token'))

  // 防止重复验证token的标志
  const isVerifyingToken = ref(false)

  // 计算属性
  const isAdmin = computed(() => userInfo.value.role === 'admin')

  // 登录方法
  const login = async (loginData) => {
    try {
      const result = await request.post('/api/auth/login', loginData)

      if (result.code === 200) {
        // 保存token和用户信息
        token.value = result.data.token
        userInfo.value = {
          ...userInfo.value,
          account: result.data.account,
          name: result.data.name || '管理员'
        }
        isLoggedIn.value = true

        // 持久化存储
        localStorage.setItem('token', result.data.token)
        localStorage.setItem('userInfo', JSON.stringify(userInfo.value))

        // 重置token过期状态
        resetTokenExpiredState()

        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(result.message || '登录失败')
        return false
      }
    } catch (error) {
      console.error('登录错误:', error)

      // 根据错误类型显示不同的提示
      if (error.response) {
        const { status, data } = error.response
        switch (status) {
          case 400:
            // 登录失败（账号或密码错误）
            ElMessage.error(data?.message || '账号或密码错误')
            break
          case 401:
            // 认证失败
            ElMessage.error('登录失败，请检查账号和密码')
            break
          case 500:
            // 服务器错误
            ElMessage.error('服务器错误，请稍后重试')
            break
          default:
            ElMessage.error(data?.message || '登录失败')
        }
      } else if (error.request) {
        // 网络错误
        ElMessage.error('网络连接失败，请检查网络后重试')
      } else {
        // 其他错误
        ElMessage.error('登录失败，请稍后重试')
      }

      return false
    }
  }

  // 登出方法
  const logout = (showMessage = true) => {
    token.value = ''
    userInfo.value = {
      account: '',
      name: '管理员',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png',
      role: 'admin'
    }
    isLoggedIn.value = false
    isVerifyingToken.value = false // 重置验证状态

    // 清除本地存储
    localStorage.removeItem('token')
    localStorage.removeItem('userInfo')

    if (showMessage) {
      ElMessage.success('已退出登录')
    }

    // 跳转到登录页
    if (router.currentRoute.value.path !== '/login') {
      router.push('/login')
    }
  }

  // 初始化用户信息（从本地存储恢复）
  const initUserInfo = () => {
    const savedToken = localStorage.getItem('token')
    const savedUserInfo = localStorage.getItem('userInfo')

    if (savedToken && savedUserInfo) {
      token.value = savedToken
      userInfo.value = JSON.parse(savedUserInfo)
      isLoggedIn.value = true
    }
  }

  // 更新用户信息
  function updateUserInfo(info) {
    userInfo.value = { ...userInfo.value, ...info }
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value))
  }

  // 检查token有效性
  const checkTokenValidity = async () => {
    if (!token.value) {
      return false
    }

    // 防止重复验证
    if (isVerifyingToken.value) {
      console.log('Token验证正在进行中，跳过重复验证')
      return false
    }

    isVerifyingToken.value = true

    try {
      const result = await request.get('/api/auth/verify')
      isVerifyingToken.value = false
      return result.code === 200
    } catch (error) {
      console.error('Token验证错误:', error)
      isVerifyingToken.value = false

      // 如果是401错误，清除登录状态
      if (error.response?.status === 401) {
        logout()
      }

      return false
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    // 计算属性
    isAdmin,
    // 方法
    login,
    logout,
    initUserInfo,
    updateUserInfo,
    checkTokenValidity
  }
})

// 布局状态store
export const useLayoutStore = defineStore('layout', () => {
  const isCollapse = ref(false)

  function toggleCollapse() {
    isCollapse.value = !isCollapse.value
  }

  function setCollapse(value) {
    isCollapse.value = value
  }

  return {
    isCollapse,
    toggleCollapse,
    setCollapse
  }
})