const { db } = require('../config/database')
const Response = require('../utils/response')

// 获取仪表盘统计数据
const getStats = async (req, res) => {
  try {
    // 从appData集合获取数据
    const appData = await getAppData()
    
    const stats = {
      totalUsers: appData.totalUsers || 0,
      totalLogined: appData.totalLogined || 0,
      totalPost: appData.totalPost || 0,
      totalView: appData.totalView || 0
    }
    
    res.json(Response.success(stats))
  } catch (error) {
    console.error('获取统计数据失败:', error)
    res.status(500).json(Response.error('获取统计数据失败'))
  }
}

// 获取所有用户数据 - 使用与SupplyList相同的分页逻辑
const getAllUsers = async (req, res) => {
  try {
    const {
      page = 1,
      pageSize = 20,
      keyword = '',
      searchType = 'nickName'
    } = req.query

    console.log('开始查询用户数据:', { page, pageSize, keyword, searchType })

    // 构建查询条件
    let query = db.collection('users')
    let countQuery = db.collection('users')

    // 如果有搜索关键词，添加搜索条件
    if (keyword && keyword.trim()) {
      let searchCondition = {}

      if (searchType === 'phoneNumber') {
        // 手机号精确匹配或部分匹配
        searchCondition['phoneNumber'] = db.RegExp({
          regexp: keyword.trim(),
          options: 'i'
        })
      } else if (searchType === 'openid') {
        // 用户ID（openid）精确匹配或部分匹配
        searchCondition['_openid'] = db.RegExp({
          regexp: keyword.trim(),
          options: 'i'
        })
      } else {
        // 昵称模糊匹配（默认）
        searchCondition['nickName'] = db.RegExp({
          regexp: keyword.trim(),
          options: 'i'
        })
      }

      query = query.where(searchCondition)
      countQuery = countQuery.where(searchCondition)
    }

    // 分页查询 - 与SupplyList保持一致
    const skip = (page - 1) * pageSize
    const result = await Promise.race([
      query
        .skip(skip)
        .limit(parseInt(pageSize))
        .orderBy('createTime', 'desc')
        .get(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('查询超时')), 15000)
      )
    ])

    // 获取总数 - 真正的总数查询而不是估算
    const countResult = await Promise.race([
      countQuery.count(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('计数查询超时')), 10000)
      )
    ])

    const total = countResult.total

    console.log(`查询成功，找到 ${result.data.length} 条用户数据，总计 ${total} 条`)

    res.json(Response.success({
      users: result.data,
      pagination: {
        total: total,
        page: parseInt(page),
        pageSize: parseInt(pageSize),
        totalPages: Math.ceil(total / parseInt(pageSize))
      }
    }))
  } catch (error) {
    console.error('获取用户列表失败:', error)
    res.status(500).json(Response.error('获取用户列表失败: ' + error.message))
  }
}

// 私有方法：从appData集合获取数据
const getAppData = async () => {
  try {
    console.log('开始从appData集合获取数据...')
    
    const result = await Promise.race([
      db.collection('appData').where({
        _id: '61493796683c0eae01a1f4045d9b0e0b'
      }).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])
    
    if (result.data && result.data.length > 0) {
      const data = result.data[0]
      console.log('成功获取appData:', data)
      return {
        totalUsers: data.totalUsers || 0,
        totalLogined: data.totalLogined || 0,
        totalPost: data.totalPost || 0,
        totalView: data.totalView || 0
      }
    } else {
      console.log('未找到appData，返回默认值')
      return {
        totalUsers: 0,
        totalLogined: 0,
        totalPost: 0,
        totalView: 0
      }
    }
  } catch (error) {
    console.error('获取appData失败:', error.message)
    // 如果获取失败，返回默认值
    return {
      totalUsers: 0,
      totalLogined: 0,
      totalPost: 0,
      totalView: 0
    }
  }
}

// 获取单个用户详情
const getUserById = async (req, res) => {
  try {
    const { userId } = req.params
    console.log('获取用户详情:', userId)
    
    const result = await Promise.race([
      db.collection('users').doc(userId).get(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])
    
    if (result.data.length > 0) {
      res.json(Response.success(result.data[0]))
    } else {
      res.status(404).json(Response.error('用户不存在'))
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    res.status(500).json(Response.error('获取用户详情失败'))
  }
}

// 更新用户信息
const updateUser = async (req, res) => {
  try {
    const { userId } = req.params
    const updateData = req.body
    
    console.log('更新用户信息:', userId, updateData)
    
    // 过滤不允许更新的字段
    const allowedFields = ['nickName', 'reward', 'province', 'remark']
    const filteredData = {}
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field]
      }
    })
    
    // 添加更新时间
    filteredData.updateTime = new Date().toISOString()
    
    const result = await Promise.race([
      db.collection('users').doc(userId).update(filteredData),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('更新超时')), 8000)
      )
    ])
    
    if (result.updated > 0) {
      // 获取更新后的用户信息
      const updatedUser = await db.collection('users').doc(userId).get()
      res.json(Response.success(updatedUser.data[0], '用户更新成功'))
    } else {
      res.status(404).json(Response.error('用户不存在或无更新'))
    }
  } catch (error) {
    console.error('更新用户失败:', error)
    res.status(500).json(Response.error('更新用户失败'))
  }
}

// 搜索用户（用于积分管理）
const searchUserForPoints = async (req, res) => {
  try {
    const { userId } = req.query

    if (!userId || !userId.trim()) {
      return res.status(400).json(Response.error('用户ID不能为空'))
    }

    console.log('搜索用户进行积分管理:', userId)

    const result = await Promise.race([
      db.collection('users').where({
        _openid: userId.trim()
      }).get(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('查询超时')), 8000)
      )
    ])

    if (result.data.length > 0) {
      const user = result.data[0]
      // 返回用户基本信息和当前积分
      const userInfo = {
        _id: user._id,
        _openid: user._openid,
        nickName: user.nickName || '未知用户',
        avatarUrl: user.avatarUrl || null,
        reward: user.reward || 0,
        phoneNumber: user.phoneNumber || '未绑定',
        createTime: user.createTime
      }
      res.json(Response.success(userInfo, '用户查找成功'))
    } else {
      res.status(404).json(Response.error('未找到该用户'))
    }
  } catch (error) {
    console.error('搜索用户失败:', error)
    res.status(500).json(Response.error('搜索用户失败: ' + error.message))
  }
}

// 个人积分操作
const updateUserPoints = async (req, res) => {
  try {
    const { userId, points, operation, reason } = req.body

    if (!userId || points === undefined || !operation) {
      return res.status(400).json(Response.error('参数不完整'))
    }

    const pointsNum = parseInt(points)
    if (isNaN(pointsNum)) {
      return res.status(400).json(Response.error('积分数量必须为数字'))
    }

    console.log('积分操作:', { userId, points: pointsNum, operation, reason })

    // 获取用户当前积分
    const userResult = await db.collection('users').where({
      _openid: userId
    }).get()

    if (userResult.data.length === 0) {
      return res.status(404).json(Response.error('用户不存在'))
    }

    const user = userResult.data[0]
    const currentPoints = user.reward || 0
    let newPoints

    if (operation === 'add') {
      newPoints = currentPoints + pointsNum
    } else if (operation === 'subtract') {
      newPoints = Math.max(0, currentPoints - pointsNum) // 积分不能为负数
    } else {
      return res.status(400).json(Response.error('无效的操作类型'))
    }

    // 更新用户积分
    const updateResult = await Promise.race([
      db.collection('users').doc(user._id).update({
        reward: newPoints,
        updateTime: new Date().toISOString()
      }),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('更新超时')), 8000)
      )
    ])

    console.log('个人积分更新结果:', JSON.stringify(updateResult, null, 2))

    // 腾讯云数据库的更新结果判断 - 只要有requestId就认为请求成功
    if (updateResult && updateResult.requestId) {
      // 记录操作日志
      await recordPointsLog({
        userId: user._openid,
        userName: user.nickName || '未知用户',
        operation,
        points: pointsNum,
        beforePoints: currentPoints,
        afterPoints: newPoints,
        reason: reason || '管理员操作',
        operatorType: 'admin',
        operateTime: new Date().toISOString()
      })

      res.json(Response.success({
        userId: user._openid,
        beforePoints: currentPoints,
        afterPoints: newPoints,
        operation,
        points: pointsNum
      }, '积分操作成功'))
    } else {
      console.error('积分更新失败 - 数据库返回结果:', JSON.stringify(updateResult, null, 2))
      res.status(500).json(Response.error(`积分更新失败 - 没有有效的requestId`))
    }
  } catch (error) {
    console.error('积分操作失败:', error)
    res.status(500).json(Response.error('积分操作失败: ' + error.message))
  }
}

// 批量积分操作
const batchUpdatePoints = async (req, res) => {
  try {
    const { points, operation, reason } = req.body

    if (points === undefined || !operation) {
      return res.status(400).json(Response.error('参数不完整'))
    }

    const pointsNum = parseInt(points)
    if (isNaN(pointsNum)) {
      return res.status(400).json(Response.error('积分数量必须为数字'))
    }

    console.log('批量积分操作:', { points: pointsNum, operation, reason })

    // 获取所有用户 - 分批获取以避免腾讯云数据库100条限制
    let allUsers = []
    let skip = 0
    const batchSize = 100 // 腾讯云数据库单次查询最大限制为100条

    console.log('开始分批获取所有用户...')

    while (true) {
      try {
        const usersResult = await Promise.race([
          db.collection('users')
            .skip(skip)
            .limit(batchSize)
            .get(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('查询超时')), 10000)
          )
        ])

        console.log(`第 ${Math.floor(skip/batchSize) + 1} 批查询结果: ${usersResult.data.length} 个用户`)

        if (usersResult.data.length === 0) {
          console.log('没有更多用户，查询结束')
          break // 没有更多用户了
        }

        allUsers = allUsers.concat(usersResult.data)
        skip += batchSize

        console.log(`累计获取用户: ${allUsers.length} 个`)

        // 如果本批获取的用户数少于批次大小，说明已经是最后一批
        if (usersResult.data.length < batchSize) {
          console.log('已获取所有用户')
          break
        }

        // 添加短暂延迟，避免频繁查询
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (error) {
        console.error(`获取第 ${Math.floor(skip/batchSize) + 1} 批用户失败:`, error)
        break
      }
    }

    const users = allUsers
    console.log(`批量操作：总共获取到 ${users.length} 个用户，准备进行积分操作`)

    if (users.length === 0) {
      return res.json(Response.success({ affectedUsers: 0 }, '没有用户需要操作'))
    }

    let successCount = 0
    let failCount = 0
    const operationLogs = []

    console.log('开始批量更新用户积分...')

    // 分批处理用户，避免单次操作过多 - 减少并发数量
    const updateBatchSize = 20 // 每批处理20个用户（降低并发压力）
    const totalBatches = Math.ceil(users.length / updateBatchSize)

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const startIndex = batchIndex * updateBatchSize
      const endIndex = Math.min(startIndex + updateBatchSize, users.length)
      const batchUsers = users.slice(startIndex, endIndex)

      console.log(`处理第 ${batchIndex + 1}/${totalBatches} 批，用户 ${startIndex + 1}-${endIndex}`)

      // 添加批次开始前的短暂延迟，让数据库有时间处理之前的请求
      if (batchIndex > 0) {
        await new Promise(resolve => setTimeout(resolve, 300))
      }

      // 并发处理当前批次的用户
      const batchPromises = batchUsers.map(async (user) => {
        try {
          const currentPoints = user.reward || 0
          let newPoints

          if (operation === 'add') {
            newPoints = currentPoints + pointsNum
          } else if (operation === 'subtract') {
            newPoints = Math.max(0, currentPoints - pointsNum)
          } else {
            throw new Error('无效的操作类型')
          }

          // 更新单个用户积分 - 增加超时时间和重试机制
          let updateResult
          let retryCount = 0
          const maxRetries = 2

          while (retryCount <= maxRetries) {
            try {
              updateResult = await Promise.race([
                db.collection('users').doc(user._id).update({
                  reward: newPoints,
                  updateTime: new Date().toISOString()
                }),
                new Promise((_, reject) =>
                  setTimeout(() => reject(new Error('更新超时')), 8000) // 增加到8秒超时
                )
              ])
              break // 成功则跳出重试循环
            } catch (error) {
              retryCount++
              if (retryCount > maxRetries) {
                throw error // 重试次数用完，抛出错误
              }
              console.log(`用户 ${user._openid} 更新失败，第 ${retryCount} 次重试...`)
              await new Promise(resolve => setTimeout(resolve, 1000)) // 重试前等待1秒
            }
          }

          // 腾讯云数据库的更新结果判断 - 只要有requestId就认为请求成功
          if (updateResult && updateResult.requestId) {
            // 记录操作日志
            return {
              success: true,
              log: {
                userId: user._openid,
                userName: user.nickName || '未知用户',
                operation,
                points: pointsNum,
                beforePoints: currentPoints,
                afterPoints: newPoints,
                reason: reason || '批量管理员操作',
                operatorType: 'admin_batch',
                operateTime: new Date().toISOString()
              }
            }
          } else {
            throw new Error('更新失败')
          }
        } catch (error) {
          console.error(`更新用户 ${user._openid} 积分失败:`, error.message)
          return { success: false, error: error.message }
        }
      })

      // 等待当前批次完成
      const batchResults = await Promise.allSettled(batchPromises)

      // 统计结果
      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          if (result.value && result.value.success) {
            successCount++
            operationLogs.push(result.value.log)
          } else {
            failCount++
          }
        } else {
          failCount++
        }
      })

      console.log(`第 ${batchIndex + 1} 批完成，当前成功: ${successCount}，失败: ${failCount}`)

      // 批次间延迟，避免数据库压力过大 - 增加延迟时间
      if (batchIndex < totalBatches - 1) {
        await new Promise(resolve => setTimeout(resolve, 500)) // 增加到500ms延迟
      }
    }

    console.log(`批量更新完成，总成功: ${successCount}，总失败: ${failCount}`)

    // 批量记录操作日志
    if (operationLogs.length > 0) {
      try {
        await recordBatchPointsLogs(operationLogs)
      } catch (error) {
        console.error('记录批量操作日志失败:', error)
      }
    }

    res.json(Response.success({
      totalUsers: users.length,
      successCount,
      failCount,
      operation,
      points: pointsNum
    }, `批量操作完成，成功 ${successCount} 个，失败 ${failCount} 个`))

  } catch (error) {
    console.error('批量积分操作失败:', error)
    res.status(500).json(Response.error('批量积分操作失败: ' + error.message))
  }
}

// 记录积分操作日志
const recordPointsLog = async (logData) => {
  try {
    await db.collection('pointsLogs').add(logData)
  } catch (error) {
    console.error('记录积分日志失败:', error)
  }
}

// 批量记录积分操作日志
const recordBatchPointsLogs = async (logsData) => {
  try {
    // 分批插入日志，避免单次操作过大
    const batchSize = 20
    for (let i = 0; i < logsData.length; i += batchSize) {
      const batch = logsData.slice(i, i + batchSize)
      await Promise.all(batch.map(log => db.collection('pointsLogs').add(log)))
    }
  } catch (error) {
    console.error('批量记录积分日志失败:', error)
  }
}

module.exports = {
  getStats,
  getAllUsers,
  getUserById,
  updateUser,
  searchUserForPoints,
  updateUserPoints,
  batchUpdatePoints
}