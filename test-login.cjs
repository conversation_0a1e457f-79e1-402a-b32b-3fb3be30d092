// 测试登录功能的脚本
const axios = require('axios')

async function testLogin() {
  try {
    console.log('开始测试登录功能...')
    
    // 测试账号1
    console.log('\n🔐 测试账号1: *********** / admin123')
    try {
      const response1 = await axios.post('http://localhost:3000/api/auth/login', {
        account: '***********',
        password: 'admin123',
        rememberMe: false
      })
      
      console.log('✅ 账号1登录成功!')
      console.log('响应:', response1.data)
    } catch (error1) {
      console.log('❌ 账号1登录失败:')
      if (error1.response) {
        console.log('状态码:', error1.response.status)
        console.log('响应数据:', error1.response.data)
      } else {
        console.log('错误信息:', error1.message)
      }
    }
    
    // 测试账号2
    console.log('\n🔐 测试账号2: liuyangtzj / *********')
    try {
      const response2 = await axios.post('http://localhost:3000/api/auth/login', {
        account: 'liuyangtzj',
        password: '*********',
        rememberMe: false
      })
      
      console.log('✅ 账号2登录成功!')
      console.log('响应:', response2.data)
    } catch (error2) {
      console.log('❌ 账号2登录失败:')
      if (error2.response) {
        console.log('状态码:', error2.response.status)
        console.log('响应数据:', error2.response.data)
      } else {
        console.log('错误信息:', error2.message)
      }
    }
    
    // 测试错误密码
    console.log('\n🔐 测试错误密码: *********** / wrongpassword')
    try {
      const response3 = await axios.post('http://localhost:3000/api/auth/login', {
        account: '***********',
        password: 'wrongpassword',
        rememberMe: false
      })
      
      console.log('意外成功:', response3.data)
    } catch (error3) {
      console.log('✅ 正确拒绝了错误密码')
      if (error3.response) {
        console.log('状态码:', error3.response.status)
        console.log('响应数据:', error3.response.data)
      }
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message)
  }
}

// 运行测试
testLogin()
