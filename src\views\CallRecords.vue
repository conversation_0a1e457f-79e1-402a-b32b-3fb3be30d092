<template>
  <div class="call-records-page">
    <div class="page-header">
      <h1 class="page-title">通话记录管理</h1>
      <p class="page-description">管理和查看用户的拨号记录数据</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Phone /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.totalCalls || 0 }}</div>
                <div class="stats-label">总通话记录</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon supply">
                <el-icon><ShoppingCart /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.supplyCalls || 0 }}</div>
                <div class="stats-label">供应中心拨打</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon demand">
                <el-icon><Sell /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.demandCalls || 0 }}</div>
                <div class="stats-label">求购中心拨打</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon today">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ stats.todayCalls || 0 }}</div>
                <div class="stats-label">今日通话</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline class="search-form">
        <el-form-item label="关键词搜索">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户ID、帖子标题、电话号码"
            clearable
            style="width: 300px"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="帖子类型">
          <el-select
            v-model="searchForm.postType"
            placeholder="选择帖子类型"
            clearable
            style="width: 150px"
            @change="handleSearch"
          >
            <el-option label="全部" value="" />
            <el-option label="供应中心" value="supply" />
            <el-option label="求购中心" value="demand" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="searchLoading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleExport" :loading="exportLoading">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <template #header>
        <div class="table-header">
          <span>通话记录列表</span>
          <div class="table-actions">
            <el-button size="small" @click="handleRefresh" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        style="width: 100%"
        :default-sort="{ prop: 'callTime', order: 'descending' }"
        @sort-change="handleSortChange"
        class="call-records-table"
      >
        <el-table-column prop="userId" label="用户ID" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="user-id-cell">
              <el-icon><User /></el-icon>
              <span>{{ row.userId }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="postId" label="帖子ID" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <el-tag size="small" type="info">{{ row.postId }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="postTypeDisplay" label="帖子类型" width="250" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="post-type-cell">
              <el-icon v-if="row.postType === 'supply'"><ShoppingCart /></el-icon>
              <el-icon v-else><Sell /></el-icon>
              <span>{{ row.postTypeDisplay }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="caller" label="拨号人" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="phone-cell">
              <el-icon><Phone /></el-icon>
              <span>{{ row.caller || '未知' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="phoneNumber" label="被拨号码" width="150" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="phone-cell">
              <el-icon><Phone /></el-icon>
              <span>{{ row.phoneNumber || '未知' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="callTime" label="拨号时间" width="180" sortable="custom">
          <template #default="{ row }">
            <div class="time-cell">
              <el-icon><Clock /></el-icon>
              <span>{{ row.callTimeFormatted || '未知' }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button size="small" type="primary" @click="handleViewDetail(row)" plain>
              <el-icon><View /></el-icon>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialogVisible"
      width="580px"
      :close-on-click-modal="false"
      class="call-record-detail-dialog"
      align-center
    >
      <template #header>
        <div class="dialog-header">
          <el-icon class="header-icon"><Phone /></el-icon>
          <span class="header-title">通话记录详情</span>
        </div>
      </template>

      <div v-if="currentRecord" class="detail-content">
        <!-- 紧凑的信息展示 -->
        <div class="info-row">
          <div class="info-item">
            <el-icon class="item-icon"><User /></el-icon>
            <div class="item-content">
              <div class="item-label">用户ID</div>
              <div class="item-value">{{ currentRecord.userId }}</div>
            </div>
          </div>
          <div class="info-item">
            <el-icon class="item-icon"><Document /></el-icon>
            <div class="item-content">
              <div class="item-label">帖子ID</div>
              <div class="item-value">{{ currentRecord.postId }}</div>
            </div>
          </div>
        </div>

        <div class="info-row full-width">
          <div class="info-item">
            <el-icon class="item-icon">
              <ShoppingCart v-if="currentRecord.postType === 'supply'" />
              <Sell v-else />
            </el-icon>
            <div class="item-content">
              <div class="item-label">帖子类型</div>
              <div class="item-value">{{ currentRecord.postTypeDisplay }}</div>
            </div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <el-icon class="item-icon"><Phone /></el-icon>
            <div class="item-content">
              <div class="item-label">拨号人</div>
              <div class="item-value">{{ currentRecord.caller || '未知' }}</div>
            </div>
          </div>
          <div class="info-item">
            <el-icon class="item-icon"><Phone /></el-icon>
            <div class="item-content">
              <div class="item-label">被拨号码</div>
              <div class="item-value">{{ currentRecord.phoneNumber || '未知' }}</div>
            </div>
          </div>
        </div>

        <div class="info-row">
          <div class="info-item">
            <el-icon class="item-icon"><Clock /></el-icon>
            <div class="item-content">
              <div class="item-label">拨号时间</div>
              <div class="item-value">{{ currentRecord.callTimeFormatted }}</div>
            </div>
          </div>
          <div class="info-item">
            <el-icon class="item-icon"><Clock /></el-icon>
            <div class="item-content">
              <div class="item-label">创建时间</div>
              <div class="item-value">{{ currentRecord.createTimeFormatted }}</div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleCopyRecord">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Phone, ShoppingCart, Sell, Calendar, Search, Refresh, Download,
  User, Clock, View, Edit, Delete, Close, Document, CopyDocument
} from '@element-plus/icons-vue'
import {
  getCallRecordsList,
  getCallRecordsStats,
  getCallRecordDetail,
  searchCallRecords,
  exportCallRecordsData,
  preloadCallRecordsData,
  clearCallRecordsCache
} from '@/api/callRecords'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const exportLoading = ref(false)
const tableData = ref([])
const stats = ref({})

// 弹窗状态
const detailDialogVisible = ref(false)
const currentRecord = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  postType: ''
})

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 排序配置
const sortConfig = reactive({
  prop: 'callTime',
  order: 'desc'
})

// 计算属性
const hasData = computed(() => tableData.value.length > 0)
const isEmpty = computed(() => !loading.value && !hasData.value)

// 获取通话记录数据的核心方法 - 企业级缓存策略
const loadCallRecordsData = async (useCache = true) => {
  loading.value = true
  try {
    // 调用真实API
    const response = await getCallRecordsList({
      ...searchForm,
      page: pagination.currentPage,
      limit: pagination.pageSize,
      sortBy: sortConfig.prop,
      sortOrder: sortConfig.order
    }, useCache)

    // 检查响应格式
    if (response && response.data) {
      tableData.value = response.data.list || []
      pagination.total = response.data.pagination?.total || 0

      console.log(`通话记录加载成功: ${tableData.value.length} 条记录`)
    } else {
      throw new Error('数据格式错误')
    }
  } catch (error) {
    ElMessage.error('数据加载失败：' + error.message)
    console.error('Load call records error:', error)
    // 清空数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatsData = async () => {
  try {
    const response = await getCallRecordsStats()
    if (response && response.data) {
      stats.value = response.data
    }
  } catch (error) {
    console.error('Load stats error:', error)
    ElMessage.error('统计数据加载失败：' + error.message)
  }
}

// 搜索处理（防抖）
const handleSearch = async () => {
  searchLoading.value = true
  try {
    pagination.currentPage = 1 // 重置到第一页
    await loadCallRecordsData(false) // 搜索不使用缓存
  } finally {
    searchLoading.value = false
  }
}

// 重置搜索
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.postType = ''
  pagination.currentPage = 1
  loadCallRecordsData()
}

// 刷新数据
const handleRefresh = () => {
  clearCallRecordsCache() // 清除缓存
  loadCallRecordsData(false) // 强制重新加载
  loadStatsData()
}

// 分页处理
const handleSizeChange = (newSize) => {
  pagination.pageSize = newSize
  pagination.currentPage = 1
  loadCallRecordsData()
}

const handleCurrentChange = (newPage) => {
  pagination.currentPage = newPage
  loadCallRecordsData()
}

// 排序处理
const handleSortChange = ({ prop, order }) => {
  sortConfig.prop = prop
  sortConfig.order = order === 'ascending' ? 'asc' : 'desc'
  loadCallRecordsData()
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const response = await getCallRecordDetail(row._id)
    if (response && response.data) {
      currentRecord.value = response.data
      detailDialogVisible.value = true
    }
  } catch (error) {
    ElMessage.error('获取详情失败：' + error.message)
  }
}

// 导出数据
const handleExport = async () => {
  exportLoading.value = true
  try {
    const response = await exportCallRecordsData({
      ...searchForm,
      sortBy: sortConfig.prop,
      sortOrder: sortConfig.order
    })

    if (response && response.data && response.data.list) {
      // 转换为CSV格式
      const csvData = convertToCSV(response.data.list)
      downloadCSV(csvData, '通话记录数据.csv')
      ElMessage.success('数据导出成功')
    }
  } catch (error) {
    ElMessage.error('导出失败：' + error.message)
  } finally {
    exportLoading.value = false
  }
}

// 转换为CSV格式
const convertToCSV = (data) => {
  const headers = ['用户ID', '帖子ID', '帖子类型', '拨号人', '被拨号码', '拨号时间', '创建时间']
  const csvContent = [
    headers.join(','),
    ...data.map(row => [
      row.userId || '',
      row.postId || '',
      row.postTypeDisplay || '',
      row.caller || '',
      row.phoneNumber || '',
      row.callTimeFormatted || '',
      row.createTimeFormatted || ''
    ].map(field => `"${field}"`).join(','))
  ].join('\n')

  return csvContent
}

// 下载CSV文件
const downloadCSV = (csvContent, filename) => {
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.setAttribute('href', url)
  link.setAttribute('download', filename)
  link.style.visibility = 'hidden'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 复制记录信息
const handleCopyRecord = async () => {
  if (!currentRecord.value) return

  const record = currentRecord.value
  const copyText = `通话记录详情
用户ID: ${record.userId}
帖子ID: ${record.postId}
帖子类型: ${record.postTypeDisplay}
拨号人: ${record.caller || '未知'}
被拨号码: ${record.phoneNumber || '未知'}
拨号时间: ${record.callTimeFormatted}
创建时间: ${record.createTimeFormatted}`

  try {
    await navigator.clipboard.writeText(copyText)
    ElMessage.success('记录信息已复制到剪贴板')
  } catch (error) {
    // 降级处理：使用传统方法复制
    const textArea = document.createElement('textarea')
    textArea.value = copyText
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('记录信息已复制到剪贴板')
  }
}

// 页面加载
onMounted(async () => {
  console.log('通话记录管理页面已加载')

  // 预加载数据
  await preloadCallRecordsData([
    { page: 1, limit: 20 }, // 预加载第一页
    { postType: 'supply', page: 1, limit: 10 }, // 预加载供应类型
    { postType: 'demand', page: 1, limit: 10 }  // 预加载求购类型
  ])

  // 加载主要数据
  await Promise.all([
    loadCallRecordsData(),
    loadStatsData()
  ])
})
</script>

<style scoped>
.call-records-page {
  min-height: 100%;
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.supply {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.demand {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #666;
  line-height: 1;
}

/* 搜索卡片样式 */
.search-card {
  margin-bottom: 16px;
  border-radius: 8px;
}

.search-form {
  margin: 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

/* 表格卡片样式 */
.table-card {
  border-radius: 8px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-actions {
  display: flex;
  gap: 8px;
}

/* 表格样式 */
.call-records-table {
  border-radius: 8px;
  overflow: hidden;
}

.call-records-table .el-table__header {
  background-color: #f8f9fa;
}

.call-records-table .el-table__row {
  transition: background-color 0.2s ease;
}

/* 表格单元格样式 */
.user-id-cell,
.phone-cell,
.time-cell,
.post-type-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.user-id-cell .el-icon,
.phone-cell .el-icon,
.time-cell .el-icon,
.post-type-cell .el-icon {
  color: #666;
  font-size: 14px;
}

.post-type-cell .el-icon {
  color: #409eff;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 详情弹窗样式 - 紧凑设计 */
.call-record-detail-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.call-record-detail-dialog .el-dialog__header {
  padding: 16px 20px;
  margin: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
}

.call-record-detail-dialog .el-dialog__body {
  padding: 20px;
  background: #fff;
}

.call-record-detail-dialog .el-dialog__footer {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.dialog-header {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.header-icon {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #667eea;
}

.info-row.full-width {
  grid-template-columns: 1fr;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.item-icon {
  width: 24px;
  height: 24px;
  background: #667eea;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
  margin-top: 2px;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  margin-bottom: 4px;
  line-height: 1;
}

.item-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  word-break: break-all;
  line-height: 1.3;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.dialog-footer .el-button {
  border-radius: 6px;
}

/* 响应式设计 - 紧凑版本 */
@media (max-width: 768px) {
  .call-record-detail-dialog {
    width: 95% !important;
    margin: 0 auto;
  }

  .call-record-detail-dialog .el-dialog__header {
    padding: 12px 16px;
  }

  .call-record-detail-dialog .el-dialog__body {
    padding: 16px;
  }

  .call-record-detail-dialog .el-dialog__footer {
    padding: 12px 16px;
  }

  .header-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }

  .header-title {
    font-size: 16px;
  }

  .detail-content {
    gap: 12px;
  }

  .info-row {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 12px;
  }

  .item-icon {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }

  .item-label {
    font-size: 11px;
  }

  .item-value {
    font-size: 13px;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 8px;
  }

  .dialog-footer .el-button {
    width: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-section .el-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .call-records-page {
    padding: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    width: 100%;
    margin-bottom: 16px;
  }

  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .pagination-container {
    justify-content: center;
  }
}

/* 深色模式支持 */
:global(.dark) .page-title {
  color: #fff;
}

:global(.dark) .page-description {
  color: #ccc;
}

:global(.dark) .stats-number {
  color: #fff;
}

:global(.dark) .stats-label {
  color: #ccc;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #666;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #999;
}

.empty-container .el-icon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #ddd;
}

.empty-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #ccc;
}
</style>
