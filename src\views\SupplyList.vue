<template>
  <div class="supply-list-page">
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">供应列表管理</h2>
        <p class="page-subtitle">高效管理和查看所有供应信息</p>
      </div>
      <div class="header-right">
        <el-card class="total-supply-card" shadow="never">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon><Box /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ totalSupplies }}</div>
              <div class="stat-label">总供应数</div>
            </div>
          </div>
        </el-card>
      </div>
    </div>





    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form">
        <div class="search-item">
          <label class="search-label">关键词搜索</label>
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索植物标题"
            clearable
            style="width: 300px"
            @clear="handleClear"
            @keydown.enter="handleSearchEnter"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="search-buttons">
          <el-button type="primary" @click="handleSearch" :loading="searchLoading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button type="success" @click="handleRefresh">
            <el-icon><RefreshRight /></el-icon>
            刷新数据
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 主数据表格 -->
    <el-card class="table-card" shadow="never">
      <template #header>
        <div class="table-header">
          <span class="table-title">供应数据列表</span>
        </div>
      </template>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        style="width: 100%"
        :height="tableHeight"
        :row-style="{ height: '80px' }"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        class="modern-table"
      >
        <el-table-column type="selection" width="55" fixed="left" />

        <!-- 图片列 -->
        <el-table-column label="产品图片" width="240" fixed="left">
          <template #default="{ row }">
            <div
              class="image-column"
              @mouseenter="handleRowMouseEnter(row)"
              @mouseleave="handleRowMouseLeave(row)"
            >
              <!-- 新格式图片 -->
              <div
                v-if="row.newImageList && row.newImageList.length > 0"
                class="images-gallery new-images"
                :class="{ 'expanded': isRowExpanded(row) }"
              >
                <div
                  v-for="(image, index) in row.newImageList"
                  :key="'new-' + index"
                  class="image-item"
                  :class="{
                    'visible': index < 1 || isRowExpanded(row),
                    'hidden': index >= 1 && !isRowExpanded(row)
                  }"
                >
                  <el-image
                    :src="processImageUrl(image.url || image)"
                    style="width: 80px; height: 80px;"
                    fit="cover"
                    :preview-src-list="getAllImages(row)"
                    :initial-index="index"
                    preview-teleported
                    class="preview-image"
                    @error="handleImageError"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>失败</span>
                      </div>
                    </template>
                    <template #placeholder>
                      <div class="image-loading">
                        <el-icon class="is-loading"><Loading /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <el-button
                    type="danger"
                    size="small"
                    :icon="Delete"
                    circle
                    class="delete-image-btn"
                    @click.stop="handleDeleteNewImage(row, index)"
                    title="删除新格式图片"
                  />
                </div>

                <!-- 显示更多新格式图片的提示 -->
                <div
                  v-if="!isRowExpanded(row) && row.newImageList.length > 1"
                  class="more-images-indicator"
                >
                  <span class="more-count">+{{ row.newImageList.length - 1 }}</span>
                </div>
              </div>

              <!-- 旧格式图片 -->
              <div
                v-if="row.imageList && row.imageList.length > 0"
                class="images-gallery old-images"
                :class="{ 'expanded': isRowExpanded(row) }"
              >
                <div
                  v-for="(url, index) in row.imageList"
                  :key="'old-' + index"
                  class="image-item"
                  :class="{
                    'visible': index < 1 || isRowExpanded(row),
                    'hidden': index >= 1 && !isRowExpanded(row)
                  }"
                >
                  <el-image
                    :src="processImageUrl(url)"
                    style="width: 80px; height: 80px;"
                    fit="cover"
                    :preview-src-list="getAllImages(row)"
                    :initial-index="(row.newImageList?.length || 0) + index"
                    preview-teleported
                    class="preview-image"
                    @error="handleImageError"
                  >
                    <template #error>
                      <div class="image-error">
                        <el-icon><Picture /></el-icon>
                        <span>失败</span>
                      </div>
                    </template>
                    <template #placeholder>
                      <div class="image-loading">
                        <el-icon class="is-loading"><Loading /></el-icon>
                      </div>
                    </template>
                  </el-image>
                  <el-button
                    type="danger"
                    size="small"
                    :icon="Delete"
                    circle
                    class="delete-image-btn"
                    @click.stop="handleDeleteOldImage(row, index)"
                    title="删除旧格式图片"
                  />
                </div>

                <!-- 显示更多旧格式图片的提示 -->
                <div
                  v-if="!isRowExpanded(row) && row.imageList.length > 1"
                  class="more-images-indicator"
                >
                  <span class="more-count">+{{ row.imageList.length - 1 }}</span>
                </div>
              </div>

              <!-- 如果两种格式的图片都不存在，显示简洁的暂无图片提示 -->
              <div
                v-if="(!row.newImageList || row.newImageList.length === 0) &&
                      (!row.imageList || row.imageList.length === 0)"
                class="no-image-placeholder-simple"
              >
                <el-icon size="16" color="#c0c4cc"><Picture /></el-icon>
                <span>无图片</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 标题列 -->
        <el-table-column label="供应标题" min-width="250" fixed="left">
          <template #default="{ row }">
            <div class="title-column" :data-row-id="row._id || row.id">
              <div v-if="!row.editingTitle" class="title-display">
                <h4 class="supply-title">{{ row.title }}</h4>
                <el-button
                  type="primary"
                  size="small"
                  :icon="Edit"
                  circle
                  class="edit-title-btn"
                  @click="startEditTitle(row)"
                  title="编辑标题"
                />
              </div>
              <div v-else class="title-edit">
                <el-input
                  v-model="row.tempTitle"
                  size="small"
                  @keyup.enter="saveTitle(row)"
                  @blur="saveTitle(row)"
                  ref="titleInput"
                  placeholder="请输入标题"
                />
                <div class="title-edit-actions">
                  <el-button
                    type="primary"
                    size="small"
                    :icon="Check"
                    @click="saveTitle(row)"
                    title="保存"
                  />
                  <el-button
                    size="small"
                    :icon="Close"
                    @click="cancelEditTitle(row)"
                    title="取消"
                  />
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="价格信息" width="160" sortable="custom">
          <template #default="{ row }">
            <div class="price-cell">
              <div class="price-main">
                <span class="price-value">¥{{ formatPrice(row.price) }}</span>
                <span class="price-unit">/{{ row.price_unit || '株' }}</span>
              </div>
              <div class="price-local" v-if="row.localPrice && row.localPrice !== row.price">
                <span class="local-label">本地价:</span>
                <span class="local-price">¥{{ formatPrice(row.localPrice) }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="联系方式" width="160">
          <template #default="{ row }">
            <div class="contact-cell">
              <div class="contact-phone" v-if="row.phoneNumber">
                <el-icon><Phone /></el-icon>
                <span>{{ row.phoneNumber }}</span>
              </div>
              <div class="contact-name" v-if="row.contactName">
                {{ row.contactName }}
              </div>
            </div>
          </template>
        </el-table-column>



        <el-table-column label="发布时间" width="140" sortable="custom">
          <template #default="{ row }">
            <div class="time-cell">
              <div class="time-main">{{ formatDate(row.createTime) }}</div>
              <div class="time-sub">{{ formatTimeOnly(row.createTime) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="发布人电话" width="160">
          <template #default="{ row }">
            <div class="publisher-phone-cell">
              <div v-if="!row.publisherPhone" class="phone-query">
                <el-button
                  type="primary"
                  size="small"
                  :loading="phoneQueryLoading[row._id || row.id]"
                  @click="queryPublisherPhone(row)"
                  :disabled="!row._openid"
                >
                  <el-icon><Phone /></el-icon>
                  查询
                </el-button>
              </div>
              <div v-else class="phone-display">
                <span class="phone-number">{{ row.publisherPhone }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click="clearPublisherPhone(row)"
                  class="clear-phone-btn"
                >
                  <el-icon><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="240" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-tooltip content="查看详情" placement="top">
                <el-button type="primary" size="small" circle @click="handleView(row)">
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="编辑信息" placement="top">
                <el-button type="warning" size="small" circle @click="handleEdit(row)">
                  <el-icon><Edit /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="用户所有供应" placement="top">
                <el-button
                  type="success"
                  size="small"
                  circle
                  @click="handleViewUserSupplies(row)"
                  :loading="userSuppliesLoading[row._id || row.id]"
                  :disabled="!row._openid"
                >
                  <el-icon><User /></el-icon>
                </el-button>
              </el-tooltip>
              <el-tooltip content="删除记录" placement="top">
                <el-button type="danger" size="small" circle @click="handleDelete(row)">
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 高级分页 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span>共 {{ pagination.total }} 条记录，第 {{ pagination.currentPage }} / {{ totalPages }} 页</span>
          <span v-if="selectedRows.length > 0" class="selection-info">
            已选择 {{ selectedRows.length }} 条记录
          </span>
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          layout="prev, pager, next, jumper"
          @current-change="handleCurrentChange"
          class="pagination-component"
        />
      </div>
    </el-card>

    <!-- 详情查看弹窗 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="供应详情"
      width="80%"
      :destroy-on-close="true"
      class="view-dialog"
      :close-on-click-modal="true"
    >
      <div v-if="currentRow" class="detail-content">
        <el-row :gutter="24">
          <el-col :span="16">
            <div class="detail-section">
              <h3>基本信息</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="供应标题">{{ currentRow.title }}</el-descriptions-item>
                <el-descriptions-item label="价格">¥{{ currentRow.price }}/{{ currentRow.price_unit }}</el-descriptions-item>
                <el-descriptions-item label="数量">{{ currentRow.quantity }}{{ currentRow.unit }}</el-descriptions-item>
                <el-descriptions-item label="品质">{{ currentRow.quality || '未设置' }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section">
              <h3>联系信息</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="联系电话">{{ currentRow.phoneNumber || currentRow.contactPhone || '未设置' }}</el-descriptions-item>
                <el-descriptions-item label="地区">
                  <div class="location-info">
                    <span v-if="!currentRow.location" class="text-gray">无位置信息</span>
                    <el-button
                      v-else
                      link
                      type="primary"
                      size="small"
                      @click="handleParseSupplyLocation(currentRow)"
                      :loading="locationParsingMap[currentRow._id || currentRow.id]"
                    >
                      <el-icon><Location /></el-icon>
                      <span v-if="locationParsingMap[currentRow._id || currentRow.id]">解析中...</span>
                      <span v-else>解析地址</span>
                    </el-button>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section" v-if="hasSpecifications(currentRow)">
              <h3>规格参数</h3>
              <el-descriptions :column="2" border>
                <!-- 基础规格参数 -->
                <el-descriptions-item label="高度" v-if="currentRow.height">{{ currentRow.height }}cm</el-descriptions-item>
                <el-descriptions-item label="米径" v-if="currentRow.meter_diameter">{{ currentRow.meter_diameter }}cm</el-descriptions-item>
                <el-descriptions-item label="分枝点" v-if="currentRow.branchPos">{{ currentRow.branchPos }}cm</el-descriptions-item>
                <el-descriptions-item label="冠幅" v-if="currentRow.canopy">{{ currentRow.canopy }}cm</el-descriptions-item>
                <el-descriptions-item label="地径" v-if="currentRow.ground_diameter">{{ currentRow.ground_diameter }}cm</el-descriptions-item>
                <el-descriptions-item label="胸径" v-if="currentRow.thorax_diameter">{{ currentRow.thorax_diameter }}cm</el-descriptions-item>

                <!-- 特殊规格参数 -->
                <el-descriptions-item label="杯口" v-if="currentRow.cup">{{ currentRow.cup }}cm</el-descriptions-item>
                <el-descriptions-item label="主蔓长度" v-if="currentRow.mainVineLength">{{ currentRow.mainVineLength }}cm</el-descriptions-item>
                <el-descriptions-item label="分支数" v-if="currentRow.branchCount">{{ currentRow.branchCount }}</el-descriptions-item>
                <el-descriptions-item label="苗龄" v-if="currentRow.plantAge">{{ currentRow.plantAge }}</el-descriptions-item>
                <el-descriptions-item label="株/m²" v-if="currentRow.plantDensity">{{ currentRow.plantDensity }}株/m²</el-descriptions-item>
                <el-descriptions-item label="丛生(杆)" v-if="currentRow.clumpCount">{{ currentRow.clumpCount }}</el-descriptions-item>
                <el-descriptions-item label="杆径" v-if="currentRow.clumpDiameter">{{ currentRow.clumpDiameter }}公分</el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="detail-section" v-if="currentRow.content">
              <h3>详细描述</h3>
              <div class="content-text">{{ currentRow.content }}</div>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="detail-section">
              <h3>产品图片</h3>
              <div class="image-gallery" v-if="getAllImages(currentRow).length > 0">
                <el-image
                  v-for="(img, index) in getAllImages(currentRow)"
                  :key="index"
                  :src="img"
                  style="width: 100px; height: 100px; margin: 5px"
                  fit="cover"
                  :preview-src-list="getAllImages(currentRow)"
                  :initial-index="index"
                  preview-teleported
                />
              </div>
              <div v-else class="no-images">暂无图片</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-dialog>

    <!-- 编辑供应弹窗 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="editMode === 'add' ? '新增供应' : '编辑供应详情'"
      width="1000px"
      :destroy-on-close="true"
      class="supply-edit-dialog"
      :modal="true"
      :lock-scroll="true"
      :close-on-click-modal="true"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
        label-position="left"
        class="supply-edit-form"
      >
        <!-- 基础信息区域 -->
        <el-card class="info-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon class="section-icon"><Document /></el-icon>
              <span class="section-title">基础信息</span>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="植物名称" prop="title">
                <el-input
                  v-model="editForm.title"
                  placeholder="请输入植物名称"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="产品质量" prop="quality">
                <el-select
                  v-model="editForm.quality"
                  placeholder="请选择产品质量"
                  size="default"
                  style="width: 100%"
                >
                  <el-option label="精品" value="精品" />
                  <el-option label="中等" value="中等" />
                  <el-option label="一般" value="一般" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="简介描述" prop="content">
                <el-input
                  v-model="editForm.content"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入植物详细描述"
                  resize="none"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 价格信息区域 -->
        <el-card class="info-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon class="section-icon"><Wallet /></el-icon>
              <span class="section-title">价格信息</span>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="上车价" prop="price">
                <el-input-number
                  v-model="editForm.price"
                  :min="0"
                  :precision="2"
                  placeholder="价格"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="价格单位" prop="price_unit">
                <el-select
                  v-model="editForm.price_unit"
                  placeholder="选择单位"
                  size="default"
                  style="width: 100%"
                >
                  <el-option label="棵" value="棵" />
                  <el-option label="株" value="株" />
                  <el-option label="公斤" value="公斤" />
                  <el-option label="斤" value="斤" />
                  <el-option label="平方米" value="平方米" />
                  <el-option label="厘米" value="厘米" />
                  <el-option label="袋" value="袋" />
                  <el-option label="捆" value="捆" />
                  <el-option label="杯" value="杯" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="供应数量" prop="quantity">
                <el-input
                  v-model="editForm.quantity"
                  placeholder="可供应数量"
                  size="default"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 规格参数区域 -->
        <el-card class="info-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon class="section-icon"><Tools /></el-icon>
              <span class="section-title">规格参数</span>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :span="8">
              <el-form-item label="高度(cm)" prop="height">
                <el-input-number
                  v-model="editForm.height"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="米径(cm)" prop="meter_diameter">
                <el-input-number
                  v-model="editForm.meter_diameter"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="胸径(cm)" prop="thorax_diameter">
                <el-input-number
                  v-model="editForm.thorax_diameter"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地径(cm)" prop="ground_diameter">
                <el-input-number
                  v-model="editForm.ground_diameter"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="冠幅(cm)" prop="canopy">
                <el-input-number
                  v-model="editForm.canopy"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="杯口(cm)" prop="cup">
                <el-input-number
                  v-model="editForm.cup"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="分枝点(cm)" prop="branchPos">
                <el-input-number
                  v-model="editForm.branchPos"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="丛生数量" prop="clumpCount">
                <el-input-number
                  v-model="editForm.clumpCount"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="杆径(cm)" prop="clumpDiameter">
                <el-input-number
                  v-model="editForm.clumpDiameter"
                  :min="0"
                  size="default"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>

        <!-- 联系信息区域 -->
        <el-card class="info-section" shadow="never">
          <template #header>
            <div class="section-header">
              <el-icon class="section-icon"><Phone /></el-icon>
              <span class="section-title">联系信息</span>
            </div>
          </template>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="联系电话" prop="phoneNumber">
                <el-input
                  v-model="editForm.phoneNumber"
                  placeholder="请输入联系电话"
                  size="default"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地址" prop="address">
                <el-input
                  v-model="editForm.address"
                  placeholder="请输入植物所在地址"
                  size="default"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-card>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            {{ editMode === 'add' ? '创建' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 位置信息弹窗 -->
    <el-dialog
      v-model="locationDialogVisible"
      title="位置信息详情"
      width="600px"
      :before-close="handleCloseLocationDialog"
      :close-on-click-modal="true"
    >
      <div v-if="locationInfo" class="location-detail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="供应标题">{{ locationInfo.supply.title }}</el-descriptions-item>
          <el-descriptions-item label="坐标">
            纬度: {{ locationInfo.coordinates.latitude.toFixed(6) }}，
            经度: {{ locationInfo.coordinates.longitude.toFixed(6) }}
          </el-descriptions-item>
          <el-descriptions-item label="详细地址">{{ locationInfo.address }}</el-descriptions-item>

          <el-descriptions-item label="地址组件" v-if="locationInfo.address_component">
            <div class="address-components">
              <el-tag v-if="locationInfo.address_component.province" type="info" size="small">
                {{ locationInfo.address_component.province }}
              </el-tag>
              <el-tag v-if="locationInfo.address_component.city" type="success" size="small">
                {{ locationInfo.address_component.city }}
              </el-tag>
              <el-tag v-if="locationInfo.address_component.district" type="warning" size="small">
                {{ locationInfo.address_component.district }}
              </el-tag>
              <el-tag v-if="locationInfo.address_component.street" size="small">
                {{ locationInfo.address_component.street }}
              </el-tag>
            </div>
          </el-descriptions-item>

          <el-descriptions-item label="附近POI" v-if="locationInfo.pois && locationInfo.pois.length > 0">
            <div class="poi-list">
              <div v-for="poi in locationInfo.pois.slice(0, 5)" :key="poi.id" class="poi-item">
                <el-icon><Location /></el-icon>
                <span>{{ poi.title }}</span>
                <small class="poi-distance">{{ poi.distance }}m</small>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <template #footer>
        <el-button type="primary" @click="handleCloseLocationDialog">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 用户所有供应弹窗 -->
    <el-dialog
      v-model="userSupplyDialogVisible"
      :title="`${currentSupplyUser?.nickName || '用户'}的所有供应信息`"
      width="1200px"
      :before-close="handleCloseUserSupplyDialog"
      class="supply-dialog"
      :modal="true"
      :lock-scroll="true"
      :close-on-click-modal="true"
    >
      <div v-if="currentSupplyUser">
        <div class="supply-header">
          <div class="supply-header-content">
            <div class="user-info">
              <el-avatar :size="40" class="user-avatar" :src="currentSupplyUser?.avatarUrl">
                <template #default>
                  {{ currentSupplyUser?.nickName?.charAt(0) || 'U' }}
                </template>
              </el-avatar>
              <div class="user-details">
                <h3 class="user-name">{{ currentSupplyUser?.nickName || '未知用户' }}</h3>
                <span class="supply-count">共 {{ userSupplies.length }} 条供应信息</span>
              </div>
            </div>
            <div class="header-actions">
              <el-button type="primary" @click="refreshUserSupplies" :loading="userSupplyLoading" size="default">
                <el-icon><Refresh /></el-icon>
                刷新数据
              </el-button>
            </div>
          </div>
        </div>

        <!-- 供应信息列表 -->
        <div v-if="userSupplies.length > 0" class="supply-list">
          <div v-for="supply in userSupplies" :key="supply._id" class="supply-item">
            <el-card class="supply-card" shadow="hover">
              <template #header>
                <div class="supply-card-header">
                  <div class="supply-title-section">
                    <el-tag type="success" size="small" class="supply-status-tag">
                      <el-icon><Document /></el-icon>
                      供应中
                    </el-tag>
                    <div class="supply-title-container">
                      <h4 class="supply-title">
                        {{ supply.title || '未命名植物' }}
                      </h4>
                    </div>
                  </div>
                  <div class="supply-actions">
                    <el-button size="small" type="primary" @click="handleEdit(supply)" plain>
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-button>
                    <el-button size="small" type="danger" @click="handleDelete(supply)" plain>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-button>
                  </div>
                </div>
              </template>

              <div class="supply-content">
                <el-row :gutter="16">
                  <!-- 基本信息区域 -->
                  <el-col :span="14">
                    <div class="supply-info-section">
                      <!-- 价格和数量信息 -->
                      <div class="price-quantity-row">
                        <div class="price-info">
                          <el-tag type="warning" size="large" class="price-tag">
                            <el-icon><Wallet /></el-icon>
                            ¥{{ supply.price || 0 }} / {{ supply.price_unit || '株' }}
                          </el-tag>
                        </div>
                        <div class="quantity-info">
                          <el-tag type="info" size="default">
                            库存: {{ supply.quantity || '0' }}
                          </el-tag>
                          <el-tag type="success" size="default" v-if="supply.quality">
                            {{ supply.quality }}
                          </el-tag>
                        </div>
                      </div>

                      <!-- 描述信息 -->
                      <div class="description-section" v-if="supply.content">
                        <p class="supply-description">{{ supply.content }}</p>
                      </div>

                      <!-- 联系信息 -->
                      <div class="contact-section">
                        <div class="contact-item" v-if="supply.phoneNumber">
                          <el-icon class="contact-icon"><Phone /></el-icon>
                          <span class="contact-text">{{ supply.phoneNumber }}</span>
                        </div>
                        <div class="contact-item" v-if="supply.address">
                          <el-icon class="contact-icon"><Location /></el-icon>
                          <span class="contact-text">{{ supply.address }}</span>
                        </div>
                      </div>
                    </div>
                  </el-col>

                  <!-- 规格参数区域 -->
                  <el-col :span="10">
                    <div class="supply-specs">
                      <div class="specs-header">
                        <el-icon class="specs-icon"><Tools /></el-icon>
                        <span class="specs-title">规格参数</span>
                      </div>
                      <div class="specs-grid">
                        <div class="spec-item" v-if="supply.height">
                          <span class="spec-label">高度</span>
                          <span class="spec-value">{{ supply.height }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.meter_diameter">
                          <span class="spec-label">米径</span>
                          <span class="spec-value">{{ supply.meter_diameter }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.thorax_diameter">
                          <span class="spec-label">胸径</span>
                          <span class="spec-value">{{ supply.thorax_diameter }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.ground_diameter">
                          <span class="spec-label">地径</span>
                          <span class="spec-value">{{ supply.ground_diameter }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.canopy">
                          <span class="spec-label">冠幅</span>
                          <span class="spec-value">{{ supply.canopy }}cm</span>
                        </div>
                        <div class="spec-item" v-if="supply.plantAge">
                          <span class="spec-label">苗龄</span>
                          <span class="spec-value">{{ supply.plantAge }}</span>
                        </div>
                      </div>
                    </div>
                  </el-col>
                </el-row>

                <!-- 新格式图片预览 -->
                <div v-if="supply.newImageList && supply.newImageList.length > 0" class="supply-images">
                  <div class="images-header">
                    <el-icon class="images-icon"><Picture /></el-icon>
                    <span class="images-title">新格式图片</span>
                    <el-tag size="small" type="success">{{ supply.newImageList.length }} 张</el-tag>
                  </div>
                  <div class="image-grid">
                    <div
                      v-for="(image, imgIndex) in supply.newImageList"
                      :key="'new-' + imgIndex"
                      class="image-item user-supply-image-item"
                    >
                      <el-image
                        :src="processImageUrl(image.url || image)"
                        :preview-src-list="getUserSupplyImages(supply)"
                        :initial-index="imgIndex"
                        preview-teleported
                        fit="cover"
                        class="supply-image"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <span>加载失败</span>
                          </div>
                        </template>
                        <template #placeholder>
                          <div class="image-loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                          </div>
                        </template>
                      </el-image>
                      <div v-if="image.captureTime" class="image-time">
                        {{ formatImageTime(image.captureTime) }}
                      </div>
                      <!-- 删除按钮 -->
                      <el-button
                        type="danger"
                        size="small"
                        :icon="Delete"
                        circle
                        class="delete-image-btn user-supply-delete-btn"
                        @click.stop="handleDeleteUserNewImage(supply, imgIndex)"
                        title="删除新格式图片"
                      />
                    </div>
                  </div>
                </div>

                <!-- 旧格式图片预览 -->
                <div v-if="supply.imageList && supply.imageList.length > 0" class="supply-images">
                  <div class="images-header">
                    <el-icon class="images-icon"><Picture /></el-icon>
                    <span class="images-title">旧格式图片</span>
                    <el-tag size="small" type="warning">{{ supply.imageList.length }} 张</el-tag>
                  </div>
                  <div class="image-grid">
                    <div
                      v-for="(url, imgIndex) in supply.imageList"
                      :key="'old-' + imgIndex"
                      class="image-item user-supply-image-item"
                    >
                      <el-image
                        :src="processImageUrl(url)"
                        :preview-src-list="getUserSupplyImages(supply)"
                        :initial-index="getOldImagePreviewIndex(supply, imgIndex)"
                        preview-teleported
                        fit="cover"
                        class="supply-image"
                      >
                        <template #error>
                          <div class="image-error">
                            <el-icon><Picture /></el-icon>
                            <span>加载失败</span>
                          </div>
                        </template>
                        <template #placeholder>
                          <div class="image-loading">
                            <el-icon class="is-loading"><Loading /></el-icon>
                          </div>
                        </template>
                      </el-image>
                      <!-- 删除按钮 -->
                      <el-button
                        type="danger"
                        size="small"
                        :icon="Delete"
                        circle
                        class="delete-image-btn user-supply-delete-btn"
                        @click.stop="handleDeleteUserOldImage(supply, imgIndex)"
                        title="删除旧格式图片"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-else-if="!userSupplyLoading" class="empty-state">
          <el-empty description="该用户暂无供应信息" />
        </div>

        <!-- 加载状态 -->
        <div v-if="userSupplyLoading" class="loading-state">
          <el-skeleton :rows="3" animated />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Edit, Check, Close, Location, Document, User, Phone, View, Refresh, Wallet, Tools, Picture, Loading } from '@element-plus/icons-vue'
import {
  getSupplyList,
  getSupplyStats,
  createSupply,
  updateSupply,
  deleteSupply,
  getUserSupplies,
  deleteSupplyImages
} from '@/api/supply'
import { reverseGeocode } from '@/api/geocoder'
import { getUserPhoneByOpenid } from '@/api/user'

// 响应式数据
const loading = ref(false)
const searchLoading = ref(false)
const saveLoading = ref(false)
const tableData = ref([])
const selectedRows = ref([])
const currentRow = ref(null)

// 图片展开状态管理
const expandedRows = ref(new Set())

// 地址解析相关状态
const locationParsingMap = ref({}) // 记录每个供应项的解析状态
const locationDialogVisible = ref(false)
const locationInfo = ref(null)

// 发布人电话查询相关状态
const phoneQueryLoading = ref({}) // 记录每个供应项的电话查询状态

// 用户所有供应相关状态
const userSuppliesLoading = ref({}) // 记录每个供应项的用户供应查询状态
const userSupplyDialogVisible = ref(false) // 用户供应弹窗显示状态
const currentSupplyUser = ref(null) // 当前查看的用户信息
const userSupplies = ref([]) // 用户的所有供应信息
const userSupplyLoading = ref(false) // 用户供应加载状态

// 弹窗状态
const viewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const editMode = ref('edit') // 'add' | 'edit'
const activeTab = ref('basic')

// 表单引用
const editFormRef = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: ''
})

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 排序配置
const sortConfig = reactive({
  prop: '',
  order: ''
})



// 编辑表单
const editForm = reactive({
  id: null,
  title: '',
  content: '',
  quality: '',
  price: 0,
  price_unit: '棵',
  quantity: '',
  phoneNumber: '',
  address: '',
  height: 0,
  meter_diameter: 0,
  thorax_diameter: 0,
  ground_diameter: 0,
  canopy: 0,
  cup: 0,
  branchPos: 0,
  clumpCount: 0,
  clumpDiameter: 0,
  imageList: [],
  newImageList: []
})

// 表单验证规则
const editRules = {
  title: [
    { required: true, message: '请输入供应标题', trigger: 'blur' },
    { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  price_unit: [
    { required: true, message: '请选择价格单位', trigger: 'change' }
  ],
  contactName: [
    { required: true, message: '请输入联系人姓名', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入地区信息', trigger: 'blur' }
  ]
}

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(pagination.total / pagination.pageSize)
})

const tableHeight = computed(() => {
  return window.innerHeight - 360
})

// 统计数据
const totalSupplies = ref(0)

// 获取数据的核心方法 - 企业级缓存策略
const loadSupplyData = async (useCache = true) => {
  loading.value = true
  try {
    // 调用真实API
    const response = await getSupplyList({
      ...searchForm,
      page: pagination.currentPage,
      pageSize: pagination.pageSize,
      sortBy: sortConfig.prop,
      sortOrder: sortConfig.order
    }, useCache)

    // 检查响应格式
    if (response && response.data) {
      tableData.value = response.data.list || []
      pagination.total = response.data.total || 0
      
      // 更新统计数据
      updateStats(tableData.value)
    } else {
      throw new Error('数据格式错误')
    }
  } catch (error) {
    ElMessage.error('数据加载失败：' + error.message)
    console.error('Load data error:', error)
    // 清空数据
    tableData.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 加载统计数据
const loadStatsData = async () => {
  try {
    const response = await getSupplyStats()
    if (response && response.data) {
      const stats = response.data
      totalSupplies.value = stats.total || 0
    }
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

// 更新统计数据
const updateStats = async (data) => {
  // 加载实时统计数据而不是从当前页面数据计算
  await loadStatsData()
}

// 动态获取API基础URL
const getImageApiBaseUrl = () => {
  // 获取当前访问的主机名
  const hostname = window.location.hostname
  const protocol = window.location.protocol

  // 如果是IP地址（局域网访问），使用相同的IP访问后端
  if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return `${protocol}//${hostname}:3000`
  }

  // 如果是localhost或其他域名，使用localhost
  return 'http://localhost:3000'
}

// 处理图片URL，确保格式正确
const processImageUrl = (url) => {
  if (!url) return ''

  // 如果已经是完整URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果是data URL，直接返回
  if (url.startsWith('data:')) {
    return url
  }

  // 处理腾讯云存储路径 cloud://
  if (url.startsWith('cloud://')) {
    const baseUrl = getImageApiBaseUrl()
    return `${baseUrl}/api/cloud-image?path=${encodeURIComponent(url)}`
  }

  // 如果是相对路径，直接返回
  if (url.startsWith('./') || url.startsWith('../')) {
    return url
  }

  // 如果是云存储文件名（包含 imageNew_ 或时间戳）
  if (url.includes('imageNew_') || url.includes('_')) {
    const baseUrl = getImageApiBaseUrl()
    return `${baseUrl}/api/cloud-image?filename=${encodeURIComponent(url)}`
  }

  // 普通文件名，拼接uploads路径
  const baseUrl = getImageApiBaseUrl()
  return `${baseUrl}/uploads/${url}`
}

// 图片处理函数
const getFirstImage = (row) => {
  if (row.newImageList && row.newImageList.length > 0) {
    const url = row.newImageList[0].url || row.newImageList[0]
    return processImageUrl(url)
  }
  if (row.imageList && row.imageList.length > 0) {
    return processImageUrl(row.imageList[0])
  }
  return null
}

const getAllImages = (row) => {
  const images = []
  if (row.newImageList && row.newImageList.length > 0) {
    images.push(...row.newImageList.map(img => processImageUrl(img.url || img)))
  }
  if (row.imageList && row.imageList.length > 0) {
    images.push(...row.imageList.map(url => processImageUrl(url)))
  }
  return images
}

// 图片展开相关函数
const getRowId = (row) => {
  return row._id || row.id
}

const isRowExpanded = (row) => {
  return expandedRows.value.has(getRowId(row))
}



const handleRowMouseEnter = (row) => {
  const rowId = getRowId(row)
  expandedRows.value.add(rowId)
}

const handleRowMouseLeave = (row) => {
  const rowId = getRowId(row)
  expandedRows.value.delete(rowId)
}

// 地址解析功能
const handleParseSupplyLocation = async (supply) => {
  if (!supply.location) {
    ElMessage.warning('该供应项没有位置信息')
    return
  }

  const supplyId = supply._id || supply.id
  // 设置解析状态
  locationParsingMap.value[supplyId] = true

  try {
    // 直接将原始geopoint数据传递给后端处理
    const response = await reverseGeocode(supply.location)

    // 检查响应格式 - 后端使用Response.success()返回的格式
    if (!response || response.code !== 200 || !response.data) {
      throw new Error(response?.message || '地址解析失败')
    }

    // 设置位置信息并显示弹窗
    locationInfo.value = {
      supply: supply,
      coordinates: response.data.coordinates,
      address: response.data.address,
      address_component: response.data.address_component,
      pois: response.data.pois || []
    }

    locationDialogVisible.value = true
    ElMessage.success('位置解析成功')

  } catch (error) {
    console.error('位置解析失败:', error)
    ElMessage.error(`位置解析失败: ${error.message}`)
  } finally {
    // 清除解析状态
    locationParsingMap.value[supplyId] = false
  }
}

// 关闭位置信息弹窗
const handleCloseLocationDialog = () => {
  locationDialogVisible.value = false
  locationInfo.value = null
}

// 查询发布人电话号码
const queryPublisherPhone = async (supply) => {
  if (!supply._openid) {
    ElMessage.warning('该供应项没有发布人信息')
    return
  }

  const supplyId = supply._id || supply.id
  // 设置查询状态
  phoneQueryLoading.value[supplyId] = true

  try {
    console.log(`开始查询发布人电话，openid: ${supply._openid}`)
    const response = await getUserPhoneByOpenid(supply._openid)

    // 检查响应格式
    if (!response || response.code !== 200 || !response.data) {
      throw new Error(response?.message || '查询发布人电话失败')
    }

    // 将电话号码设置到当前行数据中
    supply.publisherPhone = response.data.phoneNumber
    supply.publisherNickName = response.data.nickName

    ElMessage.success('发布人电话查询成功')

  } catch (error) {
    console.error('查询发布人电话失败:', error)
    ElMessage.error(`查询发布人电话失败: ${error.message}`)
  } finally {
    // 清除查询状态
    phoneQueryLoading.value[supplyId] = false
  }
}

// 清除发布人电话显示
const clearPublisherPhone = (supply) => {
  supply.publisherPhone = null
  supply.publisherNickName = null
}

// 查看用户所有供应
const handleViewUserSupplies = async (supply) => {
  if (!supply._openid) {
    ElMessage.warning('该供应项没有发布人信息')
    return
  }

  const supplyId = supply._id || supply.id
  // 设置查询状态
  userSuppliesLoading.value[supplyId] = true

  try {
    console.log(`开始查询用户所有供应，openid: ${supply._openid}`)

    // 先查询用户详细信息
    await loadUserInfo(supply._openid)

    // 显示弹窗
    userSupplyDialogVisible.value = true

    // 加载用户的所有供应信息
    await loadUserSupplies(supply._openid)

  } catch (error) {
    console.error('查询用户所有供应失败:', error)
    ElMessage.error(`查询用户所有供应失败: ${error.message}`)
  } finally {
    // 清除查询状态
    userSuppliesLoading.value[supplyId] = false
  }
}

// 加载用户详细信息
const loadUserInfo = async (openid) => {
  try {
    console.log(`开始查询用户信息，openid: ${openid}`)
    const response = await getUserPhoneByOpenid(openid)

    if (response && response.code === 200 && response.data) {
      // 设置用户信息
      currentSupplyUser.value = {
        _openid: openid,
        nickName: response.data.nickName || '未知用户',
        avatarUrl: response.data.avatarUrl || null
      }
      console.log(`用户信息查询成功: ${response.data.nickName}`, {
        nickName: response.data.nickName,
        avatarUrl: response.data.avatarUrl,
        phoneNumber: response.data.phoneNumber
      })
    } else {
      // 如果查询失败，使用默认信息
      currentSupplyUser.value = {
        _openid: openid,
        nickName: '未知用户',
        avatarUrl: null
      }
      console.log('用户信息查询失败，使用默认信息')
    }
  } catch (error) {
    console.error('查询用户信息失败:', error)
    // 查询失败时使用默认信息
    currentSupplyUser.value = {
      _openid: openid,
      nickName: '未知用户',
      avatarUrl: null
    }
  }
}

// 加载用户供应信息（复用Dashboard逻辑）
const loadUserSupplies = async (openid) => {
  try {
    userSupplyLoading.value = true
    console.log(`开始加载用户 ${openid} 的供应信息...`)

    const response = await getUserSupplies(openid)
    const result = response.data || response

    if (result && result.supplies) {
      userSupplies.value = result.supplies
      console.log(`成功加载 ${userSupplies.value.length} 条供应信息`)
    } else {
      userSupplies.value = []
      console.log('用户暂无供应信息')
    }
  } catch (error) {
    console.error('加载用户供应信息失败:', error)
    ElMessage.error('加载供应信息失败')
    userSupplies.value = []
  } finally {
    userSupplyLoading.value = false
  }
}

// 刷新用户供应信息
const refreshUserSupplies = async () => {
  if (currentSupplyUser.value && currentSupplyUser.value._openid) {
    // 重新加载用户信息和供应信息
    await loadUserInfo(currentSupplyUser.value._openid)
    await loadUserSupplies(currentSupplyUser.value._openid)
  }
}

// 关闭用户供应弹窗
const handleCloseUserSupplyDialog = () => {
  userSupplyDialogVisible.value = false
  currentSupplyUser.value = null
  userSupplies.value = []
}

// 获取供应信息的图片列表
const getSupplyImages = (supply) => {
  const images = []

  // 处理旧格式图片
  if (supply.imageList && Array.isArray(supply.imageList)) {
    supply.imageList.forEach(url => {
      if (url) {
        const processedUrl = processImageUrl(url)
        if (processedUrl) {
          images.push({ url: processedUrl, captureTime: null })
        }
      }
    })
  }

  // 处理新格式图片
  if (supply.newImageList && Array.isArray(supply.newImageList)) {
    supply.newImageList.forEach(image => {
      if (image && image.url) {
        const processedUrl = processImageUrl(image.url)
        if (processedUrl) {
          images.push({
            url: processedUrl,
            captureTime: image.captureTime
          })
        }
      }
    })
  }

  return images
}

// 获取用户供应弹窗中按显示顺序排列的图片列表（新格式在前，旧格式在后）
const getUserSupplyImages = (supply) => {
  const images = []

  // 先处理新格式图片
  if (supply.newImageList && Array.isArray(supply.newImageList)) {
    supply.newImageList.forEach(image => {
      if (image && image.url) {
        const processedUrl = processImageUrl(image.url)
        if (processedUrl) {
          images.push(processedUrl)
        }
      }
    })
  }

  // 再处理旧格式图片
  if (supply.imageList && Array.isArray(supply.imageList)) {
    supply.imageList.forEach(url => {
      if (url) {
        const processedUrl = processImageUrl(url)
        if (processedUrl) {
          images.push(processedUrl)
        }
      }
    })
  }

  return images
}

// 计算旧格式图片在用户供应弹窗预览列表中的索引
const getOldImagePreviewIndex = (supply, oldImageIndex) => {
  // 旧格式图片的索引 = 新格式图片数量 + 旧格式图片的当前索引
  const newImageCount = (supply.newImageList && Array.isArray(supply.newImageList))
    ? supply.newImageList.length
    : 0
  return newImageCount + oldImageIndex
}

// 格式化图片时间
const formatImageTime = (captureTime) => {
  if (!captureTime) return ''
  const date = new Date(captureTime)
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 删除用户供应图片
const handleDeleteUserSupplyImage = async (supply, imageIndex) => {
  try {
    // 获取图片数量并进行边界检查
    const oldImageCount = (supply.imageList && supply.imageList.length) || 0
    const newImageCount = (supply.newImageList && supply.newImageList.length) || 0
    const totalImageCount = oldImageCount + newImageCount

    // 边界检查
    if (imageIndex < 0 || imageIndex >= totalImageCount) {
      ElMessage.error(`图片索引 ${imageIndex} 超出范围 [0, ${totalImageCount - 1}]`)
      return
    }

    // 调试信息
    console.log('删除图片调试信息:', {
      imageIndex,
      oldImageCount,
      newImageCount,
      totalImageCount,
      supply: {
        imageList: supply.imageList,
        newImageList: supply.newImageList
      }
    })

    await ElMessageBox.confirm(
      '确定要删除这张图片吗？此操作不可恢复，将同时删除云端文件！',
      '删除图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 确定删除的图片类型和索引
    let imageType = ''
    let actualIndex = 0

    if (imageIndex < oldImageCount) {
      // 删除旧格式图片
      imageType = 'old'
      actualIndex = imageIndex
      console.log(`删除旧格式图片，索引: ${actualIndex}`)
    } else {
      // 删除新格式图片
      imageType = 'new'
      actualIndex = imageIndex - oldImageCount
      console.log(`删除新格式图片，原索引: ${imageIndex}, 新数组索引: ${actualIndex}`)
    }

    // 调用API删除云端文件和数据库记录
    try {
      await deleteSupplyImages(supply._id || supply.id, {
        imageIndexes: [actualIndex],
        imageType: imageType
      })

      console.log('云端文件和数据库记录删除成功')
      ElMessage.success('图片删除成功')

      // 刷新用户供应列表
      await refreshUserSupplies()

    } catch (apiError) {
      console.error('API删除失败:', apiError)
      ElMessage.error('删除图片失败：' + apiError.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除图片失败:', error)
      ElMessage.error('删除图片失败: ' + error.message)
    }
  }
}

// 获取图片总数
const getImageCount = (row) => {
  let count = 0
  if (row.newImageList && row.newImageList.length > 0) {
    count += row.newImageList.length
  }
  if (row.imageList && row.imageList.length > 0) {
    count += row.imageList.length
  }
  return count
}

// 图片错误处理
const handleImageError = (error) => {
  console.error('图片加载失败:', error)
}



// 检查是否有规格参数 - 检查所有13个苗木规格字段
const hasSpecifications = (row) => {
  return row.height || row.meter_diameter || row.branchPos ||
         row.canopy || row.ground_diameter || row.thorax_diameter ||
         row.cup || row.mainVineLength || row.branchCount ||
         row.plantAge || row.plantDensity || row.clumpCount ||
         row.clumpDiameter
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN')
}

// 格式化日期（只显示日期部分）
const formatDate = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleDateString('zh-CN')
}

// 格式化时间（只显示时间部分）
const formatTimeOnly = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
}

// 格式化价格
const formatPrice = (price) => {
  if (!price) return '0'
  return Number(price).toLocaleString('zh-CN')
}



// 事件处理方法
const handleSearch = async () => {
  searchLoading.value = true
  pagination.currentPage = 1
  await loadSupplyData(false)
  searchLoading.value = false
}

const handleSearchEnter = (event) => {
  event.preventDefault()
  handleSearch()
}

const handleClear = () => {
  // 当用户点击清空按钮时，关键字已经被清空，直接执行搜索显示所有数据
  handleSearch()
}



const handleRefresh = () => {
  loadSupplyData(false)
}

const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

const handleSortChange = ({ prop, order }) => {
  sortConfig.prop = prop
  sortConfig.order = order
  loadSupplyData(false)
}



const handleCurrentChange = (page) => {
  pagination.currentPage = page
  loadSupplyData()
}

// 操作方法
const handleView = (row) => {
  currentRow.value = row
  viewDialogVisible.value = true
}

const handleEdit = (row) => {
  editMode.value = 'edit'
  Object.assign(editForm, { ...row })
  editDialogVisible.value = true
  activeTab.value = 'basic'
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除供应"${row.title}"吗？此操作不可恢复！`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    loading.value = true
    try {
      await deleteSupply(row._id || row.id)
      ElMessage.success('删除成功')
      // 重新加载数据
      await loadSupplyData(false)

      // 如果用户供应弹窗是打开的，也刷新用户供应列表
      if (userSupplyDialogVisible.value && currentSupplyUser.value) {
        await refreshUserSupplies()
      }
    } catch (error) {
      ElMessage.error('删除失败：' + error.message)
    } finally {
      loading.value = false
    }
  } catch {
    // 用户取消
  }
}

const handleAdd = () => {
  editMode.value = 'add'
  Object.assign(editForm, {
    id: null,
    title: '',
    content: '',
    price: '',
    localPrice: '',
    price_unit: '棵',
    category: '',
    contactName: '',
    contactPhone: '',
    location: '',
    cropVariety: '',
    growthStage: '',
    quantity: '',
    unit: '株',
    plant_method: '',
    height: '',
    trunkDiameter: '',
    crownWidth: '',
    groundDiameter: '',
    chestDiameter: '',
    plantAge: '',
    imageList: [],
    newImageList: []
  })
  editDialogVisible.value = true
  activeTab.value = 'basic'
}

const handleSave = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    saveLoading.value = true

    try {
      if (editMode.value === 'add') {
        await createSupply(editForm)
        ElMessage.success('创建成功')
      } else {
        await updateSupply(editForm._id || editForm.id, editForm)
        ElMessage.success('保存成功')
      }
      
      editDialogVisible.value = false
      // 重新加载数据
      await loadSupplyData(false)
    } catch (error) {
      ElMessage.error('保存失败：' + error.message)
    } finally {
      saveLoading.value = false
    }
  } catch {
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 图片上传处理
const beforeImageUpload = (file) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件！')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB！')
    return false
  }
  
  // 模拟图片上传
  const reader = new FileReader()
  reader.onload = (e) => {
    editForm.imageList.push(e.target.result)
  }
  reader.readAsDataURL(file)
  
  return false // 阻止自动上传
}

const removeImage = (index) => {
  editForm.imageList.splice(index, 1)
}

// 用户供应弹窗 - 分离式删除新格式图片
const handleDeleteUserNewImage = async (supply, imageIndex) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张新格式图片吗？此操作不可恢复，将同时删除云端文件！',
      '删除新格式图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用API删除云端文件和数据库记录
    try {
      await deleteSupplyImages(supply._id || supply.id, {
        imageIndexes: [imageIndex],
        imageType: 'new'
      })

      console.log('用户新格式图片删除成功')
      ElMessage.success('新格式图片删除成功')

      // 刷新用户供应列表
      await refreshUserSupplies()

    } catch (apiError) {
      console.error('API删除失败:', apiError)
      ElMessage.error('删除新格式图片失败：' + apiError.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户新格式图片失败:', error)
      ElMessage.error('删除图片失败: ' + error.message)
    }
  }
}

// 用户供应弹窗 - 分离式删除旧格式图片
const handleDeleteUserOldImage = async (supply, imageIndex) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张旧格式图片吗？此操作不可恢复，将同时删除云端文件！',
      '删除旧格式图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用API删除云端文件和数据库记录
    try {
      await deleteSupplyImages(supply._id || supply.id, {
        imageIndexes: [imageIndex],
        imageType: 'old'
      })

      console.log('用户旧格式图片删除成功')
      ElMessage.success('旧格式图片删除成功')

      // 刷新用户供应列表
      await refreshUserSupplies()

    } catch (apiError) {
      console.error('API删除失败:', apiError)
      ElMessage.error('删除旧格式图片失败：' + apiError.message)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除用户旧格式图片失败:', error)
      ElMessage.error('删除图片失败: ' + error.message)
    }
  }
}

// 分离式删除新格式图片
const handleDeleteNewImage = async (row, imageIndex) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张新格式图片吗？此操作不可恢复，将同时删除云端文件！',
      '删除新格式图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用API删除云端文件和数据库记录
    try {
      await deleteSupplyImages(row._id || row.id, {
        imageIndexes: [imageIndex],
        imageType: 'new'
      })

      console.log('新格式图片删除成功')
      ElMessage.success('新格式图片删除成功')

      // 重新加载数据以确保同步
      await loadSupplyData(false)

    } catch (apiError) {
      console.error('API删除失败:', apiError)
      ElMessage.error('删除新格式图片失败：' + apiError.message)
      // 如果删除失败，重新加载数据恢复状态
      await loadSupplyData(false)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除新格式图片失败:', error)
    }
    // 用户取消或其他错误
  }
}

// 分离式删除旧格式图片
const handleDeleteOldImage = async (row, imageIndex) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这张旧格式图片吗？此操作不可恢复，将同时删除云端文件！',
      '删除旧格式图片确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    // 调用API删除云端文件和数据库记录
    try {
      await deleteSupplyImages(row._id || row.id, {
        imageIndexes: [imageIndex],
        imageType: 'old'
      })

      console.log('旧格式图片删除成功')
      ElMessage.success('旧格式图片删除成功')

      // 重新加载数据以确保同步
      await loadSupplyData(false)

    } catch (apiError) {
      console.error('API删除失败:', apiError)
      ElMessage.error('删除旧格式图片失败：' + apiError.message)
      // 如果删除失败，重新加载数据恢复状态
      await loadSupplyData(false)
    }

  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除旧格式图片失败:', error)
    }
    // 用户取消或其他错误
  }
}

// 开始编辑标题
const startEditTitle = (row) => {
  // 使用Vue的响应式方式设置编辑状态
  if (!row.editingTitle) {
    row.editingTitle = true
  }
  row.tempTitle = row.title || ''

  // 使用nextTick确保DOM更新后再聚焦
  nextTick(() => {
    // 更精确的选择器，避免选择到其他行的输入框
    const titleInput = document.querySelector(`[data-row-id="${row._id || row.id}"] .title-edit .el-input__inner`)
    if (titleInput) {
      titleInput.focus()
      titleInput.select()
    }
  })
}

// 保存标题
const saveTitle = async (row) => {
  if (!row.tempTitle || row.tempTitle.trim() === '') {
    ElMessage.warning('标题不能为空')
    return
  }

  const newTitle = row.tempTitle.trim()
  if (newTitle === row.title) {
    cancelEditTitle(row)
    return
  }

  const originalTitle = row.title

  try {
    // 先更新本地状态，提供即时反馈
    row.title = newTitle
    row.editingTitle = false
    row.tempTitle = ''

    // 调用API更新服务器
    await updateSupply(row._id || row.id, {
      title: newTitle
    })

    ElMessage.success('标题修改成功')

    // 可选：重新加载数据以确保同步（如果需要的话）
    // await loadSupplyData(false)
  } catch (error) {
    // 如果API调用失败，恢复原始状态
    row.title = originalTitle
    row.editingTitle = true
    row.tempTitle = newTitle

    ElMessage.error('修改标题失败：' + error.message)
    console.error('Update title error:', error)
  }
}

// 取消编辑标题
const cancelEditTitle = (row) => {
  row.editingTitle = false
  row.tempTitle = ''
}

// 页面加载
onMounted(async () => {
  // 并行加载数据和统计信息
  await Promise.all([
    loadSupplyData(),
    loadStatsData()
  ])
})
</script>

<style scoped>
.supply-list-page {
  min-height: 100%;
  background-color: #f5f7fa;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left {
  flex: 1;
}

.header-right {
  flex-shrink: 0;
}

.total-supply-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.total-supply-card :deep(.el-card__body) {
  padding: 16px 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.search-card {
  margin-bottom: 12px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 0;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.search-buttons {
  display: flex;
  gap: 8px;
}



.stat-card {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 总供应数卡片特定样式 */
.total-supply-card .stat-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.total-supply-card .stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.total-supply-card .stat-value {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin-bottom: 2px;
}

.total-supply-card .stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.table-card {
  margin-top: -4px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}



.title-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.title-text {
  font-weight: 500;
  color: #303133;
}

.category-tag {
  align-self: flex-start;
}

.image-preview {
  display: flex;
  justify-content: center;
  position: relative;
}

.preview-image {
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.preview-image:hover {
  transform: scale(1.05);
}

.no-image {
  color: #c0c4cc;
  font-size: 12px;
}

/* 图片列样式 */
.image-column {
  padding: 8px 0;
  /* 确保删除按钮不被裁剪 */
  overflow: visible !important;
  position: relative;
  transition: all 0.3s ease;
}

/* 分离式图片显示样式 */
.images-gallery.new-images {
  margin-bottom: 12px;
  border: 1px solid #67c23a;
  border-radius: 6px;
  padding: 8px;
  background: #f0f9ff;
}

.images-gallery.old-images {
  margin-bottom: 12px;
  border: 1px solid #e6a23c;
  border-radius: 6px;
  padding: 8px;
  background: #fefce8;
}



.images-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  align-items: flex-start;
  max-width: 220px;
  /* 为删除按钮预留空间，但不影响表格布局 */
  padding: 8px;
  margin: -4px;
  position: relative;
  transition: all 0.3s ease;
  /* 默认状态：限制高度，只显示一行 */
  max-height: 96px; /* 80px图片 + 16px padding */
  overflow: hidden;
}

/* 展开状态 */
.images-gallery.expanded {
  max-height: none;
  overflow: visible;
}

/* 更多图片指示器 */
.more-images-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 12px;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  z-index: 5;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.more-count {
  font-size: 11px;
}

/* 展开时隐藏更多指示器 */
.images-gallery.expanded .more-images-indicator {
  opacity: 0;
}

.image-item {
  position: relative;
  display: inline-block;
  margin: 4px;
  /* 确保删除按钮可以显示在图片外部 */
  overflow: visible !important;
  transition: all 0.3s ease;
}

/* 可见的图片项 */
.image-item.visible {
  opacity: 1;
  transform: scale(1);
}

/* 隐藏的图片项 */
.image-item.hidden {
  opacity: 0;
  transform: scale(0.8);
  pointer-events: none;
}

.image-item:hover .delete-image-btn {
  opacity: 1;
}

/* 当图片数量较少时，让删除按钮稍微可见 */
.images-gallery:hover .delete-image-btn {
  opacity: 0.7;
}

.images-gallery:hover .image-item:hover .delete-image-btn {
  opacity: 1;
}

.delete-image-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 28px;
  height: 28px;
  min-height: 28px;
  padding: 0;
  opacity: 0;
  transition: all 0.2s ease;
  background-color: #f56c6c;
  border-color: #f56c6c;
  /* 提高z-index确保按钮在最顶层 */
  z-index: 999;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
  cursor: pointer;
  /* 确保按钮不会被任何overflow属性影响 */
  transform: translateZ(0);
  will-change: transform, opacity;
}

.delete-image-btn:hover {
  background-color: #f78989;
  border-color: #f78989;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);
}

.delete-image-btn:active {
  transform: scale(0.95);
}

/* 标题列样式 */
.title-column {
  padding: 8px 0;
}

.title-display {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-display:hover .edit-title-btn {
  opacity: 1;
}

.edit-title-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  width: 24px;
  height: 24px;
  min-height: 24px;
  padding: 0;
}

.title-edit {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.title-edit-actions {
  display: flex;
  gap: 4px;
}

.title-edit-actions .el-button {
  width: 24px;
  height: 24px;
  min-height: 24px;
  padding: 0;
}

/* 供应信息单元格样式 - 保留用于其他地方 */
.supply-info-cell {
  display: flex;
  gap: 16px;
  align-items: center;
  padding: 8px 0;
}

.image-section {
  flex-shrink: 0;
}

.images-gallery::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.images-gallery::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.images-gallery::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.no-image-placeholder {
  width: 80px;
  height: 80px;
  border: 2px dashed #dcdfe6;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  font-size: 12px;
  gap: 4px;
}

/* 简洁的无图片提示样式 */
.no-image-placeholder-simple {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: #c0c4cc;
  font-size: 12px;
  padding: 8px;
  min-height: 32px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #f56c6c;
  font-size: 10px;
  gap: 2px;
  background-color: #fef0f0;
  border-radius: 6px;
}

.image-loading {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #409eff;
  background-color: #f0f9ff;
  border-radius: 6px;
}

.image-count-badge {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
  font-weight: 500;
}

.info-section {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}

.title-row {
  display: flex;
  align-items: center;
}

.supply-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

/* 价格单元格样式 */
.price-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price-main {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price-value {
  font-weight: 600;
  color: #e6a23c;
  font-size: 16px;
}

.price-unit {
  font-size: 12px;
  color: #909399;
}

.price-local {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.local-label {
  color: #909399;
}

.local-price {
  color: #67c23a;
  font-weight: 500;
}

/* 联系方式单元格样式 */
.contact-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-phone {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #409eff;
}

.contact-name {
  font-size: 12px;
  color: #909399;
}



/* 时间单元格样式 */
.time-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.time-main {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.time-sub {
  font-size: 11px;
  color: #909399;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: center;
}

.action-buttons .el-button {
  width: 32px;
  height: 32px;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .supply-info-cell {
    gap: 12px;
    padding: 10px 0;
  }

  .supply-title {
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  /* 图片列移动端样式 */
  .image-column .images-gallery {
    max-width: 100%;
    justify-content: flex-start;
    gap: 8px;
  }

  .image-column .el-image {
    width: 60px !important;
    height: 60px !important;
  }

  .no-image-placeholder {
    width: 60px;
    height: 60px;
    font-size: 12px;
  }

  .no-image-placeholder-simple {
    min-height: 28px;
    padding: 6px;
    font-size: 11px;
  }

  /* 标题列移动端样式 */
  .title-column .supply-title {
    font-size: 14px;
  }

  .title-edit .el-input {
    font-size: 14px;
  }

  /* 按钮调整 */
  .delete-image-btn {
    width: 24px;
    height: 24px;
    min-height: 24px;
    top: -8px;
    right: -8px;
    opacity: 0.8; /* 移动端稍微显示一些，便于点击 */
    /* 移动端也确保高z-index */
    z-index: 999;
    transform: translateZ(0);
  }

  .delete-image-btn:hover {
    opacity: 1;
  }

  /* 移动端图片列容器调整 */
  .image-column .images-gallery {
    overflow: visible !important;
    padding: 10px;
    margin: -10px;
  }

  .edit-title-btn {
    width: 20px;
    height: 20px;
    min-height: 20px;
  }

  .title-edit-actions .el-button {
    width: 20px;
    height: 20px;
    min-height: 20px;
  }

  /* 保留原有样式 */
  .supply-info-cell {
    flex-direction: column;
    gap: 10px;
    padding: 10px 0;
  }

  .image-section {
    align-self: center;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 4px;
  }

  .action-buttons .el-button {
    width: 28px;
    height: 28px;
  }
}

/* 表格行悬停效果 */
:deep(.el-table__row:hover) {
  background-color: #f8f9fa !important;
}

/* 现代表格样式 */
.modern-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: none;
}

:deep(.modern-table .el-table__header-wrapper) {
  border-radius: 8px 8px 0 0;
}

:deep(.modern-table .el-table__body-wrapper) {
  border-radius: 0 0 8px 8px;
}

:deep(.modern-table .el-table th) {
  background-color: #f8fafc;
  color: #475569;
  font-weight: 600;
  font-size: 13px;
  border: none;
  padding: 16px 12px;
}

:deep(.modern-table .el-table td) {
  border: none;
  padding: 12px;
  vertical-align: top;
}

/* 只针对包含图片列的表格单元格设置特殊样式 */
:deep(.modern-table .el-table td .image-column) {
  position: relative;
  z-index: 2;
}

/* 确保图片列的父单元格允许内容溢出 */
:deep(.modern-table .el-table__body .el-table__row .el-table__cell:nth-child(2)) {
  overflow: visible !important;
  position: relative;
  z-index: 1;
}

:deep(.modern-table .el-table__row) {
  border-bottom: 1px solid #f1f5f9;
}

:deep(.modern-table .el-table__row:last-child) {
  border-bottom: none;
}

:deep(.modern-table .el-table--striped .el-table__row--striped) {
  background-color: #f8fafc;
}

:deep(.modern-table .el-table__fixed-right) {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.06);
}

:deep(.modern-table .el-table__fixed-left) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.06);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #606266;
}

.selection-info {
  color: #409eff;
  font-weight: 500;
}

.pagination-component {
  flex-shrink: 0;
}

.view-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-left: 4px solid #409eff;
  padding-left: 12px;
}

.content-text {
  background-color: #f5f7fa;
  padding: 16px;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-images {
  text-align: center;
  color: #c0c4cc;
  padding: 40px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

/* 编辑供应弹窗样式 - 完全参考Dashboard设计 */
.supply-edit-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.supply-edit-dialog :deep(.el-dialog) {
  margin: 5vh auto !important;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.supply-edit-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  margin: 0;
  flex-shrink: 0;
}

.supply-edit-dialog :deep(.el-dialog__title) {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.supply-edit-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.supply-edit-dialog :deep(.el-dialog__body) {
  padding: 0;
  background-color: #f8fafc;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.supply-edit-dialog :deep(.el-dialog__footer) {
  flex-shrink: 0;
}

/* 供应编辑表单样式 - 参考Dashboard设计 */
.supply-edit-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
  overflow-y: auto;
}

/* 信息区块样式 - 优化后的简洁设计 */
.info-section {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.info-section:hover {
  border-color: #667eea;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.info-section :deep(.el-card__header) {
  background: #fafbfc;
  border-bottom: 1px solid #f0f2f5;
  padding: 16px 20px;
  position: relative;
}

.info-section :deep(.el-card__header)::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0 2px 2px 0;
}

.info-section :deep(.el-card__body) {
  padding: 20px;
  background: white;
}

/* 区块头部样式 - 优化后的简洁设计 */
.section-header {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  z-index: 1;
}

.section-icon {
  font-size: 18px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 6px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #2d3748;
  letter-spacing: 0.5px;
}

/* 表单项样式优化 - 完全参考Dashboard */
.supply-edit-form :deep(.el-form-item) {
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
}

.supply-edit-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #374151;
  font-size: 13px;
  line-height: 1.4;
  width: 100px !important;
  text-align: right;
  padding-right: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  min-height: 32px;
}

.supply-edit-form :deep(.el-form-item__content) {
  flex: 1;
  display: flex;
  align-items: center;
  min-height: 32px;
}

/* 文本域表单项特殊处理 */
.supply-edit-form :deep(.el-form-item:has(.el-textarea)) {
  align-items: flex-start;
}

.supply-edit-form :deep(.el-form-item:has(.el-textarea) .el-form-item__label) {
  align-items: flex-start;
  padding-top: 8px;
}

.supply-edit-form :deep(.el-form-item:has(.el-textarea) .el-form-item__content) {
  align-items: flex-start;
}

/* 输入框样式 - 完全参考Dashboard */
.supply-edit-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.supply-edit-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.2);
}

.supply-edit-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.supply-edit-form :deep(.el-textarea__inner) {
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  width: 100%;
}

.supply-edit-form :deep(.el-textarea__inner:hover) {
  box-shadow: 0 2px 6px rgba(102, 126, 234, 0.2);
}

.supply-edit-form :deep(.el-textarea__inner:focus) {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

/* 确保选择器和数字输入框也正确对齐 */
.supply-edit-form :deep(.el-select) {
  width: 100%;
}

.supply-edit-form :deep(.el-input-number) {
  width: 100%;
}

.supply-edit-form :deep(.el-input-number .el-input__wrapper) {
  width: 100%;
}



/* 响应式设计 - 移动端适配 */
@media (max-width: 768px) {
  .supply-edit-dialog :deep(.el-dialog) {
    width: 95% !important;
    margin: 2vh auto !important;
    max-height: 96vh;
  }

  .supply-edit-form {
    padding: 16px;
  }

  .info-section :deep(.el-card__body) {
    padding: 12px;
  }

  .section-title {
    font-size: 14px;
  }

  .supply-edit-form :deep(.el-form-item) {
    margin-bottom: 16px;
    flex-direction: column;
    align-items: stretch;
  }

  .supply-edit-form :deep(.el-form-item__label) {
    font-size: 13px;
    width: auto !important;
    text-align: left;
    padding-right: 0;
    padding-bottom: 4px;
    justify-content: flex-start;
    min-height: auto;
  }

  .supply-edit-form :deep(.el-form-item__content) {
    min-height: auto;
  }
}

/* 平板设备适配 */
@media (min-width: 769px) and (max-width: 1024px) {
  .supply-edit-dialog :deep(.el-dialog) {
    width: 90% !important;
  }

  .info-section :deep(.el-card__body) {
    padding: 20px;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .supply-edit-dialog :deep(.el-dialog) {
    width: 100% !important;
    max-height: 100vh;
    margin: 0 !important;
    border-radius: 0;
  }

  .supply-edit-form {
    padding: 12px;
    gap: 12px;
  }

  .info-section :deep(.el-card__header) {
    padding: 8px 12px;
  }

  .info-section :deep(.el-card__body) {
    padding: 12px;
  }

  .section-title {
    font-size: 13px;
  }

  .supply-edit-form :deep(.el-form-item) {
    margin-bottom: 12px;
    flex-direction: column;
    align-items: stretch;
  }

  .supply-edit-form :deep(.el-form-item__label) {
    font-size: 12px;
    line-height: 1.4;
    width: auto !important;
    text-align: left;
    padding-right: 0;
    padding-bottom: 4px;
    justify-content: flex-start;
    min-height: auto;
  }

  .supply-edit-form :deep(.el-form-item__content) {
    min-height: auto;
  }
}

.spec-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.spec-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.image-upload-section {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 20px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-actions {
  position: absolute;
  top: 4px;
  right: 4px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 位置信息弹窗样式 */
.location-detail {
  padding: 16px 0;
}

.address-components {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.poi-list {
  max-height: 200px;
  overflow-y: auto;
}

.poi-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
}

.poi-item:last-child {
  border-bottom: none;
}

.poi-distance {
  color: #999;
  margin-left: auto;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.location-text {
  color: #606266;
  font-size: 14px;
  max-width: 200px;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    align-self: stretch;
  }

  .page-title {
    font-size: 24px;
  }
  

  
  .search-form :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 0;
  }
  
  .pagination-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pagination-info {
    text-align: center;
    order: 2;
  }
  
  .pagination-component {
    order: 1;
  }
  
  .pagination-component :deep(.el-pagination) {
    justify-content: center;
  }
}



/* 发布人电话列样式 */
.publisher-phone-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.phone-query {
  display: flex;
  align-items: center;
  justify-content: center;
}

.phone-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #e0f2fe;
}

.phone-number {
  font-family: 'Courier New', monospace;
  font-weight: 500;
  color: #0369a1;
  font-size: 13px;
}

.clear-phone-btn {
  padding: 2px;
  min-height: auto;
  color: #64748b;
}

.clear-phone-btn:hover {
  color: #ef4444;
  background-color: #fee2e2;
}



/* 用户供应弹窗样式 */
.supply-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.supply-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

.supply-dialog :deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.supply-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.supply-dialog :deep(.el-dialog__body) {
  padding: 24px;
  max-height: 75vh;
  overflow-y: auto;
  background-color: #fafbfc;
}

/* 供应弹窗头部 */
.supply-header {
  margin-bottom: 24px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.supply-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.user-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.supply-count {
  font-size: 14px;
  color: #6b7280;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 供应信息列表 */
.supply-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.supply-item {
  width: 100%;
}

.supply-card {
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.supply-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.supply-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0;
}

.supply-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.supply-status-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
}

.supply-title-container {
  flex: 1;
}

.supply-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.supply-actions {
  display: flex;
  gap: 8px;
}

/* 供应内容区域 */
.supply-content {
  padding: 0;
}

.supply-info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-quantity-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.price-info {
  display: flex;
  align-items: center;
}

.price-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 16px;
  font-weight: 600;
  padding: 8px 16px;
}

.quantity-info {
  display: flex;
  gap: 8px;
  align-items: center;
}

.description-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid #3b82f6;
}

.supply-description {
  margin: 0;
  color: #4b5563;
  line-height: 1.6;
  font-size: 14px;
}

.contact-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
}

.contact-icon {
  color: #3b82f6;
  font-size: 16px;
}

.contact-text {
  color: #4b5563;
}

/* 规格参数区域 */
.supply-specs {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  height: 100%;
}

.specs-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.specs-icon {
  color: #3b82f6;
  font-size: 18px;
}

.specs-title {
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
}

.specs-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.spec-label {
  font-size: 13px;
  color: #6b7280;
  font-weight: 500;
}

.spec-value {
  font-size: 13px;
  color: #1f2937;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* 空状态和加载状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background: white;
  border-radius: 12px;
}

.loading-state {
  background: white;
  border-radius: 12px;
  padding: 24px;
}



/* 图片区域样式 */
.supply-images {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.images-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.images-icon {
  color: #667eea;
  font-size: 16px;
}

.images-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 12px;
}

.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.image-item:hover {
  transform: scale(1.05);
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 用户供应图片项样式 */
.user-supply-image-item {
  position: relative;
}

.user-supply-image-item:hover .user-supply-delete-btn {
  opacity: 1;
  transform: scale(1);
}

/* 用户供应删除按钮样式 */
.user-supply-delete-btn {
  position: absolute;
  top: -10px;
  right: -10px;
  width: 28px;
  height: 28px;
  min-height: 28px;
  border-radius: 50%;
  background-color: #f56565;
  border: 2px solid white;
  color: white;
  font-size: 14px;
  z-index: 100;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(245, 101, 101, 0.4);
  pointer-events: auto;
}

.user-supply-delete-btn:hover {
  background-color: #f78989;
  border-color: white;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.5);
}

.user-supply-delete-btn:active {
  transform: scale(0.95);
}

.supply-image {
  width: 100%;
  height: 100px;
  border-radius: 6px;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.supply-image:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  background-color: #f5f5f5;
  color: #999;
  font-size: 12px;
  gap: 4px;
}

.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #909399;
  font-size: 12px;
  gap: 4px;
}

.image-loading .el-icon {
  font-size: 20px;
  animation: rotate 2s linear infinite;
}

.image-error .el-icon {
  font-size: 20px;
}

.image-time {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  font-size: 10px;
  padding: 4px 6px;
  text-align: center;
}



/* 响应式图片网格 */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
  }

  .supply-image {
    height: 80px;
  }

  .image-error,
  .image-loading {
    height: 80px;
  }

  /* 移动端用户供应删除按钮适配 */
  .user-supply-delete-btn {
    width: 24px;
    height: 24px;
    min-height: 24px;
    top: -8px;
    right: -8px;
    font-size: 12px;
    opacity: 0.8;
  }

  .user-supply-delete-btn:hover {
    opacity: 1;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }  
  to {
    transform: rotate(360deg);
  }
}

/* 弹窗底部按钮样式 - 参考Dashboard */
.dialog-footer {
  padding: 16px 20px;
  background: white;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dialog-footer .el-button {
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.dialog-footer .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.dialog-footer .el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}



/* 移动端底部按钮适配 */
@media (max-width: 768px) {
  .dialog-footer {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .dialog-footer {
    padding: 12px;
    flex-direction: column;
  }

  .dialog-footer .el-button {
    width: 100%;
    margin: 0;
  }
}
</style>