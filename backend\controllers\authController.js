const jwt = require('jsonwebtoken')
const bcrypt = require('bcryptjs')
const { db } = require('../config/database')
const Response = require('../utils/response')

// JWT密钥 - 在生产环境中应该使用环境变量
const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h'

/**
 * 管理员登录
 */
const login = async (req, res) => {
  try {
    const { account, password, rememberMe } = req.body

    // 参数验证
    if (!account || !password) {
      return res.status(401).json(Response.error('登录失败！', 401))
    }

    console.log('登录尝试:', { account, rememberMe })

    // 查询管理员信息
    const adminCollection = db.collection('admin')
    const adminQuery = await adminCollection.where({
      account: account
    }).get()

    if (adminQuery.data.length === 0) {
      console.log('账号不存在:', account)
      return res.status(400).json(Response.error('账号或密码错误', 400))
    }

    const admin = adminQuery.data[0]
    console.log('找到管理员:', { account: admin.account })

    // 验证密码 (明文比较)
    console.log('密码比较:', {
      输入密码: password,
      数据库密码: admin.pw,
      是否匹配: password === admin.pw
    })

    if (password !== admin.pw) {
      console.log('密码验证失败:', account)
      return res.status(400).json(Response.error('账号或密码错误', 400))
    }

    // 生成JWT token
    const tokenExpiry = rememberMe ? '7d' : JWT_EXPIRES_IN
    const token = jwt.sign(
      { 
        id: admin._id,
        account: admin.account,
        role: 'admin'
      },
      JWT_SECRET,
      { expiresIn: tokenExpiry }
    )

    console.log('登录成功:', account)

    // 返回登录成功信息
    return res.json(Response.success({
      token,
      account: admin.account,
      name: admin.name || '管理员',
      role: 'admin'
    }, '登录成功'))

  } catch (error) {
    console.error('登录错误:', error)
    return res.status(500).json(Response.error('服务器内部错误', 500))
  }
}

/**
 * 验证token有效性
 */
const verifyToken = async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '')

    if (!token) {
      return res.status(401).json(Response.error('未提供token', 401))
    }

    // 验证token
    const decoded = jwt.verify(token, JWT_SECRET)

    // 查询管理员是否仍然存在
    const adminCollection = db.collection('admin')
    const adminQuery = await adminCollection.doc(decoded.id).get()

    if (!adminQuery.data) {
      return res.status(401).json(Response.error('token无效', 401))
    }

    return res.json(Response.success({
      account: decoded.account,
      role: decoded.role
    }, 'token有效'))

  } catch (error) {
    console.error('Token验证错误:', error)
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json(Response.error('token无效', 401))
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json(Response.error('token已过期', 401))
    }
    return res.status(500).json(Response.error('服务器内部错误', 500))
  }
}

// 注意：管理员账号需要在腾讯云开发数据库中手动创建
// admin集合结构：
// {
//   account: "admin",           // 管理员账号
//   pw: "plain_password",       // 明文密码
//   name: "管理员",             // 显示名称
//   createTime: Date,           // 创建时间
//   updateTime: Date            // 更新时间
// }
//
// 密码现在使用明文存储，直接在数据库中设置密码字符串即可

/**
 * 修改密码
 */
const changePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body
    const adminId = req.user.id

    if (!oldPassword || !newPassword) {
      return res.status(400).json(Response.error('旧密码和新密码不能为空', 400))
    }

    if (newPassword.length < 6) {
      return res.status(400).json(Response.error('新密码长度不能少于6位', 400))
    }

    // 获取当前管理员信息
    const adminCollection = db.collection('admin')
    const adminDoc = await adminCollection.doc(adminId).get()

    if (!adminDoc.data) {
      return res.status(404).json(Response.error('管理员不存在', 404))
    }

    const admin = adminDoc.data

    // 验证旧密码 (明文比较)
    console.log('旧密码比较:', {
      输入旧密码: oldPassword,
      数据库密码: admin.pw,
      是否匹配: oldPassword === admin.pw
    })

    if (oldPassword !== admin.pw) {
      return res.status(400).json(Response.error('旧密码错误', 400))
    }

    // 更新密码 (明文存储)
    await adminCollection.doc(adminId).update({
      pw: newPassword,
      updateTime: new Date()
    })

    console.log('密码修改成功:', admin.account)

    return res.json(Response.success(null, '密码修改成功'))

  } catch (error) {
    console.error('修改密码错误:', error)
    return res.status(500).json(Response.error('服务器内部错误', 500))
  }
}

module.exports = {
  login,
  verifyToken,
  changePassword
}
