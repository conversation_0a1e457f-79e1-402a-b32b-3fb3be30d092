supply_content 集合字段详细解析
基于代码分析，supply_content 集合包含以下字段：
📝 基本信息字段
字段名	类型	说明	示例
title	String	供应标题	"优质桂花树苗"
content	String	供应描述内容	"树形优美，品质优良..."
price	String/Number	价格	"50"
localPrice	String/Number	地价(本地价格)	"45"
price_unit	String	作物单位	"棵", "株", "公斤", "斤", "平方米", "厘米", "袋", "捆", "杯"
category	String	分类	"常见", "藤本类", "草皮类", "花草", "种子"
👤 联系信息字段
字段名	类型	说明	示例
contactName	String	联系人姓名	"张先生"
contactPhone	String	联系电话	"13800138000"
🌱 作物基本参数
字段名	类型	说明	示例
cropVariety	String	作物品种	"桂花"
growthStage	String	产品质量/生长阶段	"精品", "中等", "一般"
quantity	String/Number	数量	"1000"
unit	String	数量单位	"株", "盆", "斤", "公斤", "箱", "件", "包"
plant_method	String	栽培状态	"地栽苗", "小杯苗", "大杯苗", "袋装苗"
📏 苗木规格参数
字段名	类型	说明	默认显示	
height	String/Number	高度(cm)	
meter_diameter	String/Number	米径(cm)	
branchPos	String/Number	分枝点(cm)	
canopy	String/Number	冠幅(cm)	
ground_diameter	String/Number	地径(cm)	
thorax_diameter	String/Number	胸径(cm)	
cup	String/Number	杯口(cm)	
mainVineLength	String/Number	主蔓长度(cm)	
branchCount	String/Number	分支数
plantAge	String/Number	苗龄	
plantDensity	String/Number	株/m²	
clumpCount	String/Number	丛生(杆)	
clumpDiameter	String/Number	杆径(公分)	
🖼️ 图片相关字段
字段名	类型	说明
imageList	Array	图片列表(旧格式)
newImageList	Array	新图片列表(包含拍照时间和UUID)
🗺️ 地理位置字段
字段名	类型	说明	示例
location	GeoPoint	地理坐标(经纬度)	{经度: 113.123456, 纬度: 23.123456}
areaValue	Array	地区选择值	["广东省", "深圳市", "南山区"]
areaText	String	地区文本显示	"广东省深圳市南山区"
⏰ 系统字段
字段名	类型	说明
_openid	String	用户openid
createTime	Date	创建时间
updateTime	Date	更新时间
📊 状态字段
字段名	类型	说明
status	String	发布状态
isActive	Boolean	是否激活
